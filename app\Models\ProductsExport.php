<?php

namespace App\Models;

use App\Models\Product;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ProductsExport implements FromCollection, WithMapping, WithHeadings
{
    public function collection()
    {
        //return Product::all();
        
        // fetch for product stock table data
        return Product::with('productStocks')->get();
    }

    public function headings(): array
    {
        return [
            'id',	
            'name',	
            'added_by',	
            'supplier_id',	
            'user_id',	
            'category_id',	
            'brand_id',	
            'photos',	
            'thumbnail_img',	
            'video_provider',	
            'video_link',	
            'tags',	
            'description',	
            'short_description',	
            'unit_price',	
            //new
            'dropshipper_price',

            'purchase_price',	
            //new
            // 'variant_product',
            // 'attributes',

            // 'choice_options',	
            // 'colors',	
            // 'variations',	
             //new
            //  'todays_deal',

            'published',	
            'approved',	
            //new
            'stock_visibility_state',
            'cash_on_delivery',
            'featured',
            'seller_featured',
            'current_stock',

            'unit',	
            'weight',	
            'length',	
            'width',	
            'height',	
            'allow_customer_review',	
            'position',	
            //new
            'min_qty',

            'low_stock_quantity',	
            //new
            'discount',
            'discount_type',
            'discount_start_date',
            'discount_end_date',

            'tax',	
            'tax_type',	
             //new
            'shipping_type',
            'shipping_cost',
            'is_quantity_multiplied',

            'est_shipping_days',	
            //new
            // 'num_of_sale',

            'meta_title',	
            'meta_description',
            //new
            // 'meta_img',
            'pdf',

            'slug',
              //new
            'refundable',
            // 'rating',
            // 'barcode',
            // 'digital',
            // 'auction_product',
            // 'file_name',
            // 'file_path',
            // 'external_link',
            // 'external_link_btn',
            // 'wholesale_product',

            'purchase_note',
            // 'sale_price',	
            // 'regular_price',
            'shipping_class',
            // 'download_limit',	
            // 'download_expiry_day',
            'upsell',	
            'cross_sell',	
            // 'external_url',	
            // 'button_text',	
            // 'variation_swatches',
            // 'attributes_swatches',
            // 'attribute_1_name',
            // 'attribute_1_value',	
            // 'attribute_1_visible',
            // 'attribute_1_global',
            // 'attribute_1_default',
            // 'parent',
            // 'sold_individually',
            'visibility_in_catalog',
            'grouped_product',
            'backorders_allowed',
            // 'date_sale_price_start',
            // 'date_sale_price_end',
            'sku',	
            // 'price',
            // 'qty',
            // 'image',

        ];
    }

    /**
    * @var Product $product
    */
    public function map($product): array
    {
        $qty = 0;
        foreach ($product->stocks as $key => $stock) {
            $qty += $stock->qty;
        }


        // Convert photo codes to URLs
        $photoUrls = $this->convertPhotoUrls($product->photos);
        $thumbnailUrl = $this->getPhotoUrl($product->thumbnail_img);


        return [
            $product->id,	
            $product->name,	
            $product->added_by,	
            $product->supplier_id,	
            $product->user_id,	
            $product->category_id,	
            $product->brand_id,	

            'photos' => implode(',', $photoUrls), // Convert array to comma-separated string
            'thumbnail_img' => $thumbnailUrl,

            $product->video_provider,	
            $product->video_link,	
            $product->tags,	
            $product->description,	
            $product->short_description,	
            $product->unit_price,	
              //new
            $product->dropshipper_price,

            $product->purchase_price,	
            //new
            // $product->variant_product,
            // $product->attributes,
            // $product->choice_options,	
            // $product->colors,
            // $product->variations,	
             //new
            // $product->todays_deal,

            $product->published,	
            $product->approved,	
              //new
            $product->stock_visibility_state,
            $product->cash_on_delivery,
            $product->featured,
            $product->seller_featured,
            $product->current_stock,
 
            $product->unit,	
            $product->weight,	
            $product->length,	
            $product->width,	
            $product->height,	
            $product->allow_customer_review,	
            $product->position,	
              //new
            $product->min_qty,

            $product->low_stock_quantity,
              //new
            $product->discount,
            $product->discount_type,
            $product->discount_start_date,
            $product->discount_end_date,

            $product->tax,	
            $product->tax_type,	
               //new
            $product->shipping_type,
            $product->shipping_cost,
            $product->is_quantity_multiplied,

            $product->est_shipping_days,	
              //new
            // $product->num_of_sale,

            $product->meta_title,	
            $product->meta_description,	
            //new
            // $product->meta_img,
            $product->pdf,

            $product->slug,	
              //new
            $product->refundable,
            // $product->rating,
            // $product->barcode,
            // (string) $product->digital,
            // $product->auction_product,
            // $product->file_name,
            // $product->file_path,
            // $product->external_link,
            // $product->external_link_btn,
            // $product->wholesale_product,

            $product->purchase_note,	
            // $product->sale_price,
            // $product->regular_price,	
            $product->shipping_class,	
            // $product->download_limit,	
            // $product->download_expiry_day,	
            $product->upsell,	
            $product->cross_sell,	
            // $product->external_url,	
            // $product->button_text,	
            // $product->variation_swatches,	
            // $product->attributes_swatches,	
            // $product->attribute_1_name,	
            // $product->attribute_1_value,	
            // $product->attribute_1_visible,	
            // $product->attribute_1_global,	
            // $product->attribute_1_default,	
            // $product->parent,	
            // $product->sold_individually,	
            $product->visibility_in_catalog,	
            $product->grouped_product,	
            $product->backorders_allowed,	
            // $product->date_sale_price_start,	
            // $product->date_sale_price_end,	
      

            // Map product stocks
            $product->productStocks->map(function ($stock) {
                return [
                    $stock->sku,
                ];
            })->toArray()

        ];
    }


    // Convert photo IDs to URLs
    private function convertPhotoUrls($photoIds)
    {
        $photoUrls = [];
        $ids = explode(',', $photoIds);
        
        foreach ($ids as $id) {
            $url = $this->getPhotoUrl($id);
            if ($url) {
                $photoUrls[] = $url;
            }
        }
        
        return $photoUrls;
    }

    // Get photo URL by ID
    private function getPhotoUrl($id)
    {
        $upload = Upload::find($id);
        if ($upload) {
            return $upload->external_link;
        }
        return null;
    }

    



}
