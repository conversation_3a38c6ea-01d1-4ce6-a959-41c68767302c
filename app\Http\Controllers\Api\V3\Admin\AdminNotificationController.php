<?php

namespace App\Http\Controllers\Api\V3\Admin;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use App\Services\NotificationService;
use App\Services\NotificationDashboardService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;

class AdminNotificationController extends Controller
{
    protected $notificationService;
    protected $dashboardService;

    public function __construct(NotificationService $notificationService, NotificationDashboardService $dashboardService)
    {
        $this->notificationService = $notificationService;
        $this->dashboardService = $dashboardService;
    }

    /**
     * Get admin notifications with filters and pagination
     */
    public function getAdminNotifications(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'page' => 'integer|min:1',
                'per_page' => 'integer|min:1|max:100',
                'type' => 'string|in:NEW_ORDER,PAYMENT_RECEIVED,SUPPORT_TICKET,USER_REGISTERED,STOCK_ALERT,RETURN_REQUEST,SYSTEM',
                'priority' => 'string|in:LOW,MEDIUM,HIGH,URGENT',
                'status' => 'string|in:unread,read',
                'date_from' => 'date',
                'date_to' => 'date|after_or_equal:date_from',
                'search' => 'string|max:255'
            ]);

            $query = Notification::where('user_type', 'admin')
                ->orderBy('created_at', 'desc');

            // Apply filters
            if ($request->has('type')) {
                $query->where('type', $request->type);
            }

            if ($request->has('priority')) {
                $query->where('priority', $request->priority);
            }

            if ($request->has('status')) {
                if ($request->status === 'unread') {
                    $query->whereNull('read_at');
                } else {
                    $query->whereNotNull('read_at');
                }
            }

            if ($request->has('date_from')) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to')) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }

            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('message', 'like', "%{$search}%");
                });
            }

            $perPage = $request->get('per_page', 20);
            $notifications = $query->paginate($perPage);

            // Get stats
            $stats = $this->getNotificationStats();

            return response()->json([
                'success' => true,
                'data' => [
                    'notifications' => $notifications->items(),
                    'pagination' => [
                        'current_page' => $notifications->currentPage(),
                        'per_page' => $notifications->perPage(),
                        'total' => $notifications->total(),
                        'last_page' => $notifications->lastPage(),
                        'has_more_pages' => $notifications->hasMorePages()
                    ],
                    'stats' => $stats
                ]
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch notifications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get notification statistics for admin dashboard
     */
    public function getNotificationStats(): array
    {
        $today = Carbon::today();
        $yesterday = Carbon::yesterday();
        $weekAgo = Carbon::now()->subWeek();

        return [
            'total_unread' => Notification::where('user_type', 'admin')
                ->whereNull('read_at')->count(),
            'today_count' => Notification::where('user_type', 'admin')
                ->whereDate('created_at', $today)->count(),
            'yesterday_count' => Notification::where('user_type', 'admin')
                ->whereDate('created_at', $yesterday)->count(),
            'week_count' => Notification::where('user_type', 'admin')
                ->where('created_at', '>=', $weekAgo)->count(),
            'by_type' => Notification::where('user_type', 'admin')
                ->select('type', DB::raw('count(*) as count'))
                ->groupBy('type')
                ->pluck('count', 'type'),
            'by_priority' => Notification::where('user_type', 'admin')
                ->select('priority', DB::raw('count(*) as count'))
                ->groupBy('priority')
                ->pluck('count', 'priority'),
            'urgent_unread' => Notification::where('user_type', 'admin')
                ->where('priority', 'URGENT')
                ->whereNull('read_at')
                ->count()
        ];
    }

    /**
     * Mark admin notification as read
     */
    public function markAsRead(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'notification_id' => 'required|integer|exists:notifications,id'
            ]);

            $notification = Notification::where('id', $request->notification_id)
                ->where('user_type', 'admin')
                ->firstOrFail();

            if (!$notification->read_at) {
                $notification->update(['read_at' => now()]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Notification marked as read',
                'data' => $notification
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark notification as read',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark all admin notifications as read
     */
    public function markAllAsRead(): JsonResponse
    {
        try {
            $updated = Notification::where('user_type', 'admin')
                ->whereNull('read_at')
                ->update(['read_at' => now()]);

            return response()->json([
                'success' => true,
                'message' => "Marked {$updated} notifications as read",
                'data' => ['updated_count' => $updated]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark all notifications as read',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete admin notification
     */
    public function deleteNotification(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'notification_id' => 'required|integer|exists:notifications,id'
            ]);

            $notification = Notification::where('id', $request->notification_id)
                ->where('user_type', 'admin')
                ->firstOrFail();

            $notification->delete();

            return response()->json([
                'success' => true,
                'message' => 'Notification deleted successfully'
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete notification',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk delete admin notifications
     */
    public function bulkDelete(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'notification_ids' => 'required|array',
                'notification_ids.*' => 'integer|exists:notifications,id'
            ]);

            $deleted = Notification::whereIn('id', $request->notification_ids)
                ->where('user_type', 'admin')
                ->delete();

            return response()->json([
                'success' => true,
                'message' => "Deleted {$deleted} notifications",
                'data' => ['deleted_count' => $deleted]
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete notifications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear all read admin notifications
     */
    public function clearRead(): JsonResponse
    {
        try {
            $deleted = Notification::where('user_type', 'admin')
                ->whereNotNull('read_at')
                ->delete();

            return response()->json([
                'success' => true,
                'message' => "Cleared {$deleted} read notifications",
                'data' => ['deleted_count' => $deleted]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear read notifications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send test notification to admin
     */
    public function sendTestNotification(): JsonResponse
    {
        try {
            $this->notificationService->sendNotification(
                null, // admin notification
                'admin',
                'Test Notification',
                'This is a test notification to verify the system is working correctly.',
                'SYSTEM',
                'LOW',
                null,
                ['test' => true, 'timestamp' => now()]
            );

            return response()->json([
                'success' => true,
                'message' => 'Test notification sent successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send test notification',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get recent urgent notifications for admin
     */
    public function getUrgentNotifications(): JsonResponse
    {
        try {
            $notifications = Notification::where('user_type', 'admin')
                ->where('priority', 'URGENT')
                ->whereNull('read_at')
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            return response()->json([
                'success' => true,
                'data' => $notifications
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch urgent notifications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get notification dashboard data
     */
    public function getDashboard(): JsonResponse
    {
        try {
            $dashboardData = $this->dashboardService->getDashboardData();

            return response()->json([
                'success' => true,
                'data' => $dashboardData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch dashboard data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get unread notifications count for admin bell icon
     */
    public function getUnreadCount(): JsonResponse
    {
        try {
            $count = Notification::where('user_type', 'admin')
                ->whereNull('read_at')
                ->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'unread_count' => $count
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch unread count',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get recent unread notifications for admin bell dropdown
     */
    public function getRecentUnread(): JsonResponse
    {
        try {
            $notifications = Notification::where('user_type', 'admin')
                ->whereNull('read_at')
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get()
                ->map(function ($notification) {
                    return [
                        'id' => $notification->id,
                        'title' => $notification->title,
                        'message' => $notification->message,
                        'type' => $notification->type,
                        'priority' => $notification->priority,
                        'created_at' => $notification->created_at,
                        'time_ago' => $notification->created_at->diffForHumans(),
                        'action_url' => $notification->action_url,
                        'action_text' => $notification->action_text,
                        'metadata' => $notification->metadata
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $notifications
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch recent unread notifications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get bell icon summary data (count + recent notifications)
     */
    public function getBellIconData(): JsonResponse
    {
        try {
            $unreadCount = Notification::where('user_type', 'admin')
                ->whereNull('read_at')
                ->count();

            $recentNotifications = Notification::where('user_type', 'admin')
                ->whereNull('read_at')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get()
                ->map(function ($notification) {
                    return [
                        'id' => $notification->id,
                        'title' => $notification->title,
                        'message' => Str::limit($notification->message, 80),
                        'type' => $notification->type,
                        'priority' => $notification->priority,
                        'created_at' => $notification->created_at,
                        'time_ago' => $notification->created_at->diffForHumans(),
                        'action_url' => $notification->action_url,
                        'action_text' => $notification->action_text,
                        'icon' => $this->getNotificationIcon($notification->type),
                        'color' => $this->getNotificationColor($notification->priority)
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => [
                    'unread_count' => $unreadCount,
                    'recent_notifications' => $recentNotifications,
                    'has_urgent' => Notification::where('user_type', 'admin')
                        ->where('priority', 'URGENT')
                        ->whereNull('read_at')
                        ->exists()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch bell icon data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark notification as read from bell icon
     */
    public function markAsReadFromBell(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'notification_id' => 'required|integer|exists:notifications,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $notification = Notification::where('id', $request->notification_id)
                ->where('user_type', 'admin')
                ->firstOrFail();

            $notification->update(['read_at' => now()]);

            // Get updated count
            $unreadCount = Notification::where('user_type', 'admin')
                ->whereNull('read_at')
                ->count();

            return response()->json([
                'success' => true,
                'message' => 'Notification marked as read',
                'data' => [
                    'unread_count' => $unreadCount
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark notification as read',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get notification icon based on type
     */
    private function getNotificationIcon(string $type): string
    {
        $icons = [
            'NEW_ORDER' => 'shopping-cart',
            'PAYMENT_RECEIVED' => 'credit-card',
            'SUPPORT_TICKET' => 'help-circle',
            'USER_REGISTERED' => 'user-plus',
            'STOCK_ALERT' => 'alert-triangle',
            'RETURN_REQUEST' => 'rotate-ccw',
            'PRODUCT_REVIEW' => 'star',
            'SYSTEM' => 'settings'
        ];

        return $icons[$type] ?? 'bell';
    }

    /**
     * Get notification color based on priority
     */
    private function getNotificationColor(string $priority): string
    {
        $colors = [
            'LOW' => 'text-gray-500',
            'MEDIUM' => 'text-blue-500',
            'HIGH' => 'text-orange-500',
            'URGENT' => 'text-red-500'
        ];

        return $colors[$priority] ?? 'text-gray-500';
    }
} 