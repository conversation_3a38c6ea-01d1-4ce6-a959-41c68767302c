<?php

namespace App\Http\Controllers\Api\V3;

use App\Enums\OrderStatus;
use App\Http\Controllers\AffiliateController;
use App\Http\Controllers\Controller;
use App\Http\Resources\V3\Orders\OrderResource;
use App\Http\Resources\V3\Orders\OrdersResource;
use App\Models\Address;
use App\Models\Cart;
use App\Models\CartInfo;
use App\Models\CombinedOrder;
use App\Models\Coupon;
use App\Models\CouponUsage;
use App\Models\Order;
use App\Models\OrderCancel;
use App\Models\OrderDetail;
use App\Models\Product;
use App\Models\StripeCard;
use App\Models\User;
use App\Notifications\order\OrderCancelRequestSendNotification;
use App\Services\ActivityLogService;
use App\Services\ApiOrderService;
use App\Services\OrderService;
use App\Services\StripeService;
use App\Utility\NotificationUtility;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\JsonResponse;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\QueryException;
use App\Http\Resources\V3\BulkOrder\ProductsForBulkOrderResource;
use App\Http\Resources\V3\BulkOrder\ProductBulkOrderDetailResource;
use Stripe\Exception\ApiErrorException;
use Stripe\Exception\CardException;
use Stripe\Exception\InvalidRequestException;
use App\Services\OrderProcessingService;
use App\Helpers\NotificationHelper;

class ApiOrdersController extends ApiResponse
{
    /**
     * @var \App\Services\OrderService
     */
    protected OrderService $orderService;
    protected StripeService $stripeService;
    protected ActivityLogService $activityLogService;
    protected ApiOrderService $apiOrderService;
    protected OrderProcessingService $orderProcessingService;
    public function __construct(
        StripeService $stripeService,
        OrderService $orderService,
        ActivityLogService $activityLogService,
        ApiOrderService $apiOrderService,
        OrderProcessingService $orderProcessingService
    ) {
        parent::__construct();
        $this->stripeService = $stripeService;
        $this->orderService = $orderService;
        $this->activityLogService = $activityLogService;
        $this->apiOrderService = $apiOrderService;
        $this->orderProcessingService = $orderProcessingService;
    }
    //Log::channel('api_order')->error('Error in user social login : AuthController ' . print_r($e->getMessage(),true));
    public function order_list(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'sort' => 'nullable|in:date_desc,date_asc,total_desc,total_asc',
            'start_date' => 'nullable|date_format:Y-m-d',
            'end_date' => 'nullable|date_format:Y-m-d|after_or_equal:start_date',
            'search' => 'nullable|string|max:255',
            'status' => 'nullable|string|in:' . implode(',', OrderStatus::getValues())
        ]);
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        try {
            $per_page = min((int) $request->input('per_page', 20), 50);
            $page = max((int) $request->input('page', 1), 1);
            $sort = $request->input('sort', 'date_desc');
            $start_date = $request->input('start_date');
            $end_date = $request->input('end_date');
            $search = $request->input('search');

            $query = Order::with('orderDetails', 'orderDetails.product', 'user')->where('user_id', auth()->user()->id);

            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('code', 'like', '%' . $search . '%')
                        ->orWhereHas('user', function ($q) use ($search) {
                            $q->where('name', 'like', '%' . $search . '%');
                        })
                        ->orWhereHas('orderDetails.product', function ($q) use ($search) {
                            $q->where('name', 'like', '%' . $search . '%');
                        });
                });
            }

            if ($start_date) {
                $query->whereDate('created_at', '>=', $start_date);
            }

            if ($end_date) {
                $query->whereDate('created_at', '<=', $end_date);
            }

            if ($request->has('status')) {
                $query->where('delivery_status', $request->status);
            }

            switch ($sort) {
                case 'date_asc':
                    $query->orderBy('created_at', 'asc');
                    break;
                case 'total_desc':
                    $query->orderBy('grand_total', 'desc');
                    break;
                case 'total_asc':
                    $query->orderBy('grand_total', 'asc');
                    break;
                default: // date_desc
                    $query->orderBy('created_at', 'desc');
            }

            $orders = $query->paginate($per_page);
            $total_items = $orders->total();
            $total_pages = $orders->lastPage();

            $data = [
                'orders' => new OrdersResource($orders),
                'pagination' => [
                    'currentPage' => (int) $page,
                    'totalPages' => $total_pages,
                    'totalItems' => $total_items,
                    'itemsPerPage' => (int) $per_page,
                ]
            ];
            return $this->success($data);
        } catch (QueryException $e) {
            Log::channel('api_order')->error('Unexpected error while fetching orders list : ApiOrdersController ' . print_r($e->getMessage(), true));
            return $this->error('A database error occurred. Please try again later.', 500);
        } catch (ModelNotFoundException $e) {
            Log::channel('api_order')->error('Unexpected error while fetching orders list : ApiOrdersController ' . print_r($e->getMessage(), true));
            return $this->error('Requested resource not found.', 404);
        } catch (\Exception $e) {
            Log::channel('api_order')->error('Unexpected error while fetching orders list : ApiOrdersController ' . print_r($e->getMessage(), true));
            return $this->error('Something went wrong. Please try again later.', 500);
        }
    }

    public function get_order_details(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'order_code' => 'required|string|exists:orders,code',
        ]);
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        try {
            $order = Order::with([
                'orderDetails.product', 
                'user',
                'carrier',
                'activityLogs'
            ])
            ->where('code', $request->order_code)
            ->where('user_id', auth()->user()->id)
            ->first();
            if (!$order) {
                return $this->error('Requested resource not found.', 404);
            }

            return $this->success(new OrderResource($order));

        } catch (QueryException $e) {
            Log::channel('api_order')->error('Unexpected error while fetching orders list : ApiOrdersController ' . print_r($e->getMessage(), true));
            return $this->error('A database error occurred. Please try again later.', 500);
        } catch (ModelNotFoundException $e) {
            Log::channel('api_order')->error('Unexpected error while fetching orders list : ApiOrdersController ' . print_r($e->getMessage(), true));
            return $this->error('Requested resource not found.', 404);
        } catch (\Exception $e) {
            Log::channel('api_order')->error('Unexpected error while fetching orders list in ApiOrdersController', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
            ]);
            return $this->error('Something went wrong. Please try again later.', 500);
        }


    }
    public function get_order_status(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'order_code' => 'required|string|exists:orders,code',
        ]);
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        try {
            $order = Order::with('orderDetails', 'orderDetails.product', 'user')
                ->where('code', $request->order_code)
                ->first();
            if (!$order) {
                return $this->error('Requested resource not found.', 404);
            }
            $response =[
                "orderId"=> (string) $order->code,
                "status"=> OrderStatus::getLabel($order->delivery_status),
                "createdAt"=> $order->created_at->toIso8601String(),
                "paymentStatus"=> $order->payment_status,
                "fulfillmentStatus"=> "",
                "trackingNumber"=> $order->carrier_tracking_code,
                "trackingUrl"=> $order->tracking_link,
                "estimatedDelivery"=> ""
            ];
            return $this->success($response);

        } catch (QueryException $e) {
            Log::channel('api_order')->error('Unexpected error while fetching orders list : ApiOrdersController ' . print_r($e->getMessage(), true));
            return $this->error('A database error occurred. Please try again later.', 500);
        } catch (ModelNotFoundException $e) {
            Log::channel('api_order')->error('Unexpected error while fetching orders list : ApiOrdersController ' . print_r($e->getMessage(), true));
            return $this->error('Requested resource not found.', 404);
        } catch (\Exception $e) {
            Log::channel('api_order')->error('Unexpected error while fetching orders list in ApiOrdersController', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
            ]);
            return $this->error('Something went wrong. Please try again later.', 500);
        }


    }

    public function save_order(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'items' => 'nullable|array',
            'items.*.productId' => 'required_with:items|exists:products,id',
            'items.*.quantity' => 'required_with:items|integer|min:1',
            'items.*.price' => 'required_with:items|numeric|min:0',
            'shippingAddressId' => 'nullable|exists:addresses,id',
            'paymentMethodId' => 'nullable|string',
            'payment_intent_id' => 'nullable|string',
            'subtotal' => 'nullable|numeric|min:0',
            'shipping' => 'nullable|numeric|min:0',
            'tax' => 'nullable|numeric|min:0',
            'discount' => 'nullable|numeric|min:0',
            'total' => 'nullable|numeric|min:0',
            'order_from' => 'nullable|in:web,android,ios',
            'additional_info' => 'nullable|string',
            'is_gift' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        $user_id = auth()->user()->id;
        $order_from = $request->input('order_from', 'web');
        $additional_info = $request->input('additional_info');
        $is_gift = $request->input('is_gift', 0);
        $payment_intent_id = $request->input('payment_intent_id');

        // Handle two scenarios: 
        // 1. Frontend sends items directly (new approach)
        // 2. Items are already in cart (existing approach)
        
        if ($request->has('items') && !empty($request->items)) {
            // New approach: Frontend sends items directly
            
            // Use provided address or get from cart
            $address_id = $request->shippingAddressId;
            if (!$address_id) {
                $cartInfo = CartInfo::where('user_id', $user_id)->first();
                $address_id = $cartInfo?->address_id;
            }
            
            if (!$address_id) {
                return $this->validation_error(
                    400,
                    'Please provide shipping address',
                    [],
                    400
                );
            }

            $address = Address::where('id', $address_id)
                ->where('user_id', $user_id)
                ->first();
                
            if (!$address) {
                return $this->validation_error(
                    400,
                    'Invalid shipping address',
                    [],
                    400
                );
            }

            $shipping_address = $this->apiOrderService->formatShippingAddress($address);

            // Convert frontend items to cart format for processing
            $items = collect($request->items)->map(function ($item) use ($user_id) {
                $product = Product::find($item['productId']);
                if (!$product) {
                    throw new \Exception("Product not found: {$item['productId']}");
                }
                
                return (object) [
                    'id' => $item['productId'],
                    'product_id' => $item['productId'], 
                    'user_id' => $user_id,
                    'qty' => $item['quantity'],
                    'price' => $item['price'],
                    'product' => $product,
                    'choices' => [],
                ];
            });

            // Group by seller
            $seller_products = $this->apiOrderService->groupProductsBySeller($items);

            DB::beginTransaction();
            try {
                $combined_order = $this->apiOrderService->createCombinedOrder($user_id, $shipping_address);
                $combined_order_code = $combined_order->combined_order_code;
                
                // Store payment intent ID if provided
                if ($payment_intent_id) {
                    $combined_order->payment_intent_id = $payment_intent_id;
                    $combined_order->payment_status = 'paid';
                }
                
                $i = 1;

                foreach ($seller_products as $seller => $seller_product) {
                    $order_code = count($seller_products) > 1 ? $combined_order_code . '-' . $i++ : $combined_order_code;
                    $order = $this->apiOrderService->createOrder($order_code, $combined_order->id, $user_id, $shipping_address, $additional_info, $is_gift, $order_from, $seller);
                    $this->apiOrderService->processOrderDetails($order, $seller_product);
                    $this->apiOrderService->updateOrderTotals($order, $seller_product);
                    $this->apiOrderService->processCouponIfExists($order, $seller_product, $user_id);
                    $combined_order->grand_total += $order->grand_total;
                    
                    // Update order payment status if payment was processed
                    if ($payment_intent_id) {
                        $order->payment_status = 'paid';
                        $order->save();
                    }
                }

                $combined_order->save();

                // Log successful order creation
                $this->activityLogService->log(
                    'order_created',
                    'Order created successfully',
                    $combined_order->id,
                    CombinedOrder::class,
                    $user_id,
                    User::class,
                    '',
                    OrderStatus::PENDING,
                    null,
                    null
                );

                // Send notification to admin about new order
                NotificationHelper::orderCreated($combined_order);

                // Clear user's cart after successful order if payment was processed
                if ($payment_intent_id) {
                    try {
                        Cart::where('user_id', $user_id)->delete();
                        CartInfo::where('user_id', $user_id)->delete();
                    } catch (\Exception $e) {
                        Log::channel('api_order')->warning('Failed to clear cart after order: ' . $e->getMessage());
                    }
                }

                DB::commit();

                $response = [
                    'id' => $combined_order->combined_order_code,
                    'order_id' => $combined_order->combined_order_code,
                    'combined_order_id' => $combined_order->id,
                    'total' => $combined_order->grand_total,
                    'status' => $payment_intent_id ? 'paid' : 'pending',
                    'payment_intent_id' => $payment_intent_id,
                ];

                return $this->success(
                    $response,
                    'Order placed successfully',
                );

            } catch (\Exception $e) {
                Log::channel('api_order')->error('Error in save_order (new approach): ' . $e->getMessage());
                DB::rollBack();
                return $this->error(
                    'INTERNAL_SERVER_ERROR',
                    $e->getMessage(),
                    500
                );
            }
        } else {
            // Existing approach: Items are in cart database
            $cartInfo = CartInfo::where('user_id', $user_id)->first();
            if (empty($cartInfo)) {
                return $this->validation_error(
                    400,
                    'Please add product to cart',
                    [],
                    400
                );
            }
            
            $carts = Cart::where('user_id', $user_id)->get();
            if ($carts->isEmpty()) {
                return $this->validation_error(
                    400,
                    'Please add product to cart',
                    [],
                    400
                );
            }

            $address = Address::where('id', $cartInfo->address_id)
                ->where('user_id', $user_id)
                ->first();
            if (empty($address)) {
                return $this->validation_error(
                    400,
                    'Please add address',
                    [],
                    400
                );
            }
            
            $shipping_address = $this->apiOrderService->formatShippingAddress($address);
            $seller_products = $this->apiOrderService->groupProductsBySeller($carts);
            
            DB::beginTransaction();
            try {
                $combined_order = $this->apiOrderService->createCombinedOrder($user_id, $shipping_address);
                $combined_order_code = $combined_order->combined_order_code;
                
                // Store payment intent ID if provided
                if ($payment_intent_id) {
                    $combined_order->payment_intent_id = $payment_intent_id;
                    $combined_order->payment_status = 'paid';
                }
                
                $i = 1;
                
                foreach ($seller_products as $seller => $seller_product) {
                    $order_code = count($seller_products) > 1 ? $combined_order_code . '-' . $i++ : $combined_order_code;
                    $order = $this->apiOrderService->createOrder($order_code, $combined_order->id, $user_id, $shipping_address, $additional_info, $is_gift, $order_from, $seller);
                    $this->apiOrderService->processOrderDetails($order, $seller_product);
                    $this->apiOrderService->updateOrderTotals($order, $seller_product);
                    $this->apiOrderService->processCouponIfExists($order, $seller_product, $user_id);
                    $combined_order->grand_total += $order->grand_total;
                    
                    // Update order payment status if payment was processed
                    if ($payment_intent_id) {
                        $order->payment_status = 'paid';
                        $order->save();
                    }
                }
                
                $combined_order->save();

                // Log successful order creation
                $this->activityLogService->log(
                    'order_created',
                    'Order created successfully',
                    $combined_order->id,
                    CombinedOrder::class,
                    $user_id,
                    User::class,
                    '',
                    OrderStatus::PENDING,
                    null,
                    null
                );

                // Send notification to admin about new order
                NotificationHelper::orderCreated($combined_order);

                // Clear user's cart after successful order if payment was processed
                if ($payment_intent_id) {
                    try {
                        Cart::where('user_id', $user_id)->delete();
                        CartInfo::where('user_id', $user_id)->delete();
                    } catch (\Exception $e) {
                        Log::channel('api_order')->warning('Failed to clear cart after order: ' . $e->getMessage());
                    }
                }
                
                $response = [
                    'id' => $combined_order->combined_order_code,
                    'order_id' => $combined_order->combined_order_code,
                    'combined_order_id' => $combined_order->id,
                    'total' => $combined_order->grand_total,
                    'status' => $payment_intent_id ? 'paid' : 'pending',
                    'payment_intent_id' => $payment_intent_id,
                ];
                
                return $this->success(
                    $response,
                    'Order placed successfully',
                );

            } catch (\Exception $e) {
                Log::channel('api_order')->error('Error in save_order (existing approach): ' . $e->getMessage());
                DB::rollBack();
                return $this->error(
                    'INTERNAL_SERVER_ERROR',
                    $e->getMessage(),
                    500
                );
            }
        }
    }

    public function bulk_order_products(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'nullable|integer|min:1',
            'limit' => 'nullable|integer|min:1|max:50',
            'search' => 'nullable|string|max:255',
            'category' => 'nullable|string|exists:categories,slug',
            'sort' => 'nullable|string|in:price-asc,price-desc,name-asc,name-desc,stock-asc,stock-desc',
            'inStock' => 'nullable|boolean'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            $query = Product::with(['category', 'brand', 'stocks'])
                ->where('published', 1)
                ->where('auction_product', 0)
                ->where('approved', 1);

            // Apply search filter
            if ($request->search) {
                $query->where(function ($q) use ($request) {
                    $q->where('name', 'like', '%' . $request->search . '%')
                        ->orWhere('description', 'like', '%' . $request->search . '%');
                });
            }

            // Apply category filter
            if ($request->category) {
                $query->whereHas('category', function ($q) use ($request) {
                    $q->where('slug', $request->category);
                });
            }

            // Apply stock filter
            if ($request->has('inStock')) {
                $query->whereHas('stocks', function ($q) use ($request) {
                    if ($request->inStock) {
                        $q->where('qty', '>', 0);
                    }
                });
            }

            // Apply sorting
            if ($request->sort) {
                [$field, $direction] = explode('-', $request->sort);
                switch ($field) {
                    case 'price':
                        $query->orderBy('unit_price', $direction);
                        break;
                    case 'name':
                        $query->orderBy('name', $direction);
                        break;
                    case 'stock':
                        $query->orderBy('current_stock', $direction);
                        break;
                }
            }

            $limit = $request->input('limit', 10);
            $products = $query->paginate($limit);

            return $this->success([
                'products' => ProductsForBulkOrderResource::collection($products),
                'meta' => [
                    'current_page' => $products->currentPage(),
                    'from' => $products->firstItem(),
                    'last_page' => $products->lastPage(),
                    'path' => $request->url(),
                    'per_page' => $products->perPage(),
                    'to' => $products->lastItem(),
                    'total' => $products->total()
                ],
                'links' => [
                    'first' => $products->url(1),
                    'last' => $products->url($products->lastPage()),
                    'prev' => $products->previousPageUrl(),
                    'next' => $products->nextPageUrl()
                ]
            ]);

        } catch (QueryException $e) {
            Log::channel('api_order')->error('Database error in bulk_order_products: ' . $e->getMessage());
            return $this->error('A database error occurred. Please try again later.', 500);
        } catch (\Exception $e) {
            Log::channel('api_order')->error('Unexpected error in bulk_order_products: ' . $e->getMessage());
            return $this->error('Something went wrong. Please try again later.', 500);
        }
    }
    public function bulk_order_product_details(Request $request, $slug)
    {
        try {
            $product = Product::with(['category', 'brand', 'stocks'])
                ->where('slug', $slug)
                ->where('published', 1)
                ->where('approved', 1)
                ->where('auction_product', 0)
                ->firstOrFail();
            return $this->success(new ProductBulkOrderDetailResource($product));


        } catch (ModelNotFoundException $e) {
            return $this->error('Product not found', 404);
        } catch (\Exception $e) {
            Log::channel('api_order')->error('Unexpected error in bulk_order_product_details: ' . $e->getMessage());
            return $this->error('Something went wrong. Please try again later.', 500);
        }
    }
    public function bulk_order_save(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'items' => 'required|array',
            'items.*.product_id' => 'required|string|exists:products,slug',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.notes' => 'nullable|string',
            'address_id' => 'required|exists:addresses,id',
            'order_from' => 'required|in:web,android,ios',
            'paymentMethod' => 'required|exists:stripe_cards,stripe_card_id',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        $paymentMethod = $request->paymentMethod;
        $additional_info = $request->additional_info;
        $pickup_point = $request->pickup_point;
        $shipping_type = $request->shipping_type;
        $order_from = $request->order_from;
        $carrier_id = $request->carrier_id;
        $user_id = auth()->user()->id;
        $user = auth()->user();
        $is_gift = 0;

        $address = Address::where('id', $request->address_id)
            ->where('user_id', $user_id)
            ->first();

        if (empty($address)) {
            return $this->validation_error(
                400,
                'Invalid address',
                [],
                400
            );
        }
        DB::beginTransaction();
        try {
            $shipping_address = $this->apiOrderService->formatShippingAddress($address);
            $combined_order = $this->apiOrderService->createCombinedOrder($user_id, $shipping_address);
            $seller_products = $this->apiOrderService->groupProductsBySeller($request->items);
            $combined_order_code = $combined_order->combined_order_code;
            foreach ($seller_products as $seller_product) {
                $order_code = count($seller_products) > 1 ? $combined_order_code . '-' . $i++ : $combined_order_code;
                $order = $this->apiOrderService->createOrder($order_code, $combined_order->id, $user_id, $shipping_address, $additional_info, $is_gift, $order_from, $seller);
                $this->apiOrderService->processOrderDetails($order, $seller_product);
                $this->apiOrderService->updateOrderTotals($order, $seller_product);
                $this->apiOrderService->processCouponIfExists($order, $seller_product, $user_id);

                $combined_order->grand_total += $order->grand_total;
            }
            $combined_order->save();
            DB::commit();
            $paymentIntent = $this->apiOrderService->makePaymentIntent(
                $combined_order,
                $paymentMethod,
                $user->stripeCustomer->stripe_customer_id,
                $user->email,
                '',
                ''
            );
            if ($paymentIntent->status !== 'succeeded') {
                Log::channel('api_stripe_payment')->error(
                    'Payment failed',
                    [
                        'order_code' => $combined_order->combined_order_code,
                        'user_id' => $user->id,
                        'payment_intent_status' => $paymentIntent->status
                    ]
                );
                return $this->error('PAYMENT_FAILED', 'Payment was not successful', null, 400);
            }

            $card_details = StripeCard::where('stripe_card_id', $request->paymentMethod)->first();
            $paymentStatusUpdate = $this->apiOrderService->processPayment(
                $paymentIntent,
                $combined_order,
                $card_details
            );
            if (!$paymentStatusUpdate) {
                return $this->error(
                    'ORDER_PROCESSING_ERROR',
                    'Error processing order after payment',
                    null,
                    500
                );
            }
            return $this->success(
                [
                    'message' => 'Order placed successfully',
                    'orderId' => $combined_order->combined_order_code,
                    'status' => OrderStatus::ORDER_PLACED,
                    'totalAmount' => $combined_order->grand_total,
                    'estimatedDelivery' => '',
                    'paymentStatus' => 'paid',
                    'trackingNumber' => null,
                ],
                'Order placed successfully',
                200
            );
        } catch (\Exception $e) {
            DB::rollBack();
            Log::channel('api_order')->error('Error in bulk order save: ' . $e->getMessage());
            return $this->error(
                'INTERNAL_SERVER_ERROR',
                'Something went wrong while processing your order',
                500
            );
        }
    }
    public function dropshipper_orders(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'page' => 'nullable|integer|min:1',
            'limit' => 'nullable|integer|min:1|max:50',
            'status' => 'nullable|string|in:pending,processing,shipped,delivered,cancelled,refunded',
            'search' => 'nullable|string|max:255',
            'start_date' => 'nullable|date_format:Y-m-d',
            'end_date' => 'nullable|date_format:Y-m-d|after_or_equal:start_date',
            'sort_by' => 'nullable|string|in:created_at,total,status',
            'sort_order' => 'nullable|string|in:asc,desc',
            'bulk_orders_only' => 'nullable|boolean'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            $limit = $request->input('limit', 20);
            $bulkOrdersOnly = $request->input('bulk_orders_only', false);
            
            $query = Order::with(['user', 'orderDetails']);
            
            // For bulk orders, filter by user_id (dropshipper's orders)
            // For regular dropshipper orders, filter by seller_id
            if ($bulkOrdersOnly) {
                $query->where('user_id', auth()->id())
                      ->where('is_bulk_order', 1);
            } else {
                $query->where(function($q) {
                    $q->where('seller_id', auth()->id())
                      ->orWhere(function($subQ) {
                          $subQ->where('user_id', auth()->id())
                               ->where('is_bulk_order', 1);
                      });
                });
            }

            // Apply search filter
            if ($request->search) {
                $query->where(function ($q) use ($request) {
                    $q->where('code', 'like', '%' . $request->search . '%')
                        ->orWhereHas('user', function ($q) use ($request) {
                            $q->where('name', 'like', '%' . $request->search . '%');
                        });
                });
            }

            // Apply status filter
            if ($request->status) {
                $query->where('delivery_status', $request->status);
            }

            // Apply date filters
            if ($request->start_date) {
                $query->whereDate('created_at', '>=', $request->start_date);
            }
            if ($request->end_date) {
                $query->whereDate('created_at', '<=', $request->end_date);
            }

            // Apply sorting
            $sort_by = $request->input('sort_by', 'created_at');
            $sort_order = $request->input('sort_order', 'desc');

            switch ($sort_by) {
                case 'total':
                    $query->orderBy('grand_total', $sort_order);
                    break;
                case 'status':
                    $query->orderBy('delivery_status', $sort_order);
                    break;
                default:
                    $query->orderBy('created_at', $sort_order);
            }

            $orders = $query->paginate($limit);

            $formatted_orders = $orders->map(function ($order) {
                $shipping_address = json_decode($order->shipping_address);
                return [
                    'id' => $order->code,
                    'order_id' => $order->id,
                    'is_bulk_order' => (bool) $order->is_bulk_order,
                    'order_type' => $order->is_bulk_order ? 'bulk' : 'regular',
                    'customer' => [
                        'id' => $order->user_id,
                        'name' => $order->user->name,
                        'email' => $order->user->email
                    ],
                    'order_date' => $order->created_at,
                    'status' => $order->delivery_status,
                    'payment_status' => $order->payment_status,
                    'tracking_number' => $order->tracking_number,
                    'tracking_url' => $order->tracking_url,
                    'item_count' => $order->orderDetails->sum('quantity'),
                    'subtotal' => $order->orderDetails->sum('price'),
                    'shipping' => $order->orderDetails->sum('shipping_cost'),
                    'tax' => $order->orderDetails->sum('tax'),
                    'total' => $order->grand_total,
                    'commission' => $order->commission,
                    'commission_status' => $order->commission_status,
                    'shipping_address' => [
                        'name' => $shipping_address->name ?? '',
                        'line1' => $shipping_address->street_address ?? '',
                        'line2' => $shipping_address->apartment_address ?? '',
                        'city' => $shipping_address->city ?? '',
                        'state' => $shipping_address->state ?? '',
                        'postal_code' => $shipping_address->postal_code ?? '',
                        'country' => $shipping_address->country ?? ''
                    ],
                    'estimated_delivery' => $order->estimated_delivery
                ];
            });

            return $this->success([
                'orders' => $formatted_orders,
                'pagination' => [
                    'total' => $orders->total(),
                    'count' => $orders->count(),
                    'per_page' => $orders->perPage(),
                    'current_page' => $orders->currentPage(),
                    'total_pages' => $orders->lastPage(),
                    'links' => [
                        'next' => $orders->nextPageUrl()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::channel('api_order')->error('Error in dropshipper orders: ' . $e->getMessage());
            return $this->error(
                'INTERNAL_SERVER_ERROR',
                'Something went wrong while fetching orders',
                500
            );
        }
    }
    public function cancelOrder(Request $request, string $orderId): JsonResponse
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'reason' => 'required|string|in:changed_mind,found_better_price,delivery_too_slow,product_not_needed,ordered_by_mistake,other',
                'comments' => 'nullable|string|max:500'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            // Get the authenticated user
            $user = auth()->user();

            if (!$user) {
                return $this->error(
                    'UNAUTHORIZED',
                    'Authentication required',
                    null,
                    401
                );
            }

            // Get the order
            $order = Order::where('code', $orderId)
                ->where('user_id', $user->id)
                ->first();
            $order_old_status = $order->delivery_status;
            if (!$order) {
                return $this->error(
                    'NOT_FOUND',
                    'Order not found or does not belong to the authenticated user',
                    null,
                    404
                );
            }
            $orderCancel = OrderCancel::where('order_id', $order->id)->first();
            if ($orderCancel) {
                return $this->error(
                    'ALREADY_CANCELLED',
                    'You have already requested to cancel this order',
                    null,
                    400
                );

            }
            $orderCancel = new OrderCancel();
            $orderCancel->user_id = Auth::user()->id;
            $orderCancel->order_id = $order->id;
            $orderCancel->seller_id = 0;
            $orderCancel->seller_approval = 0;
            $orderCancel->admin_approval = 0;
            $orderCancel->cancel_amount = $order->grand_total;
            $orderCancel->reason = $request->reason;
            $orderCancel->cancel_comments = $request->comments;
            $orderCancel->admin_seen = 0;
            $orderCancel->status = 0;
            $orderCancel->reject_reason = Null;
            $orderCancel->save();
            // Check if the order is in a cancellable state
            $this->activityLogService->log(
                'order_status_changed',
                'Customer Cancelled Order Request',
                $order->id,
                Order::class,
                auth()->user()->id,
                get_class(auth()->user()),
                $order_old_status,
                OrderStatus::CANCELLED,
                null,
                email_end_time: null,
            );
            try {
                $array = array();
                $array['order_code'] = $order->code;
                $array['user_name'] = $order->user->name;
                $array['subject'] = translate('Confirmation of Your Order Cancellation Request ') . ' - ' . $order->code;
                $order->user->notify(new OrderCancelRequestSendNotification($array));
                $command = 'php ' . base_path() . '/artisan queue:work --verbose --tries=1 --timeout=220 > /dev/null &';
                exec($command);
            } catch (\Exception $e) {
                Log::channel('email_logs')->error('Error occurred while sending OrderCancelRequestSendNotification email in OrderCancelController : ' . $e->getMessage());
            }
            // Return response
            return $this->success([
                'data' => [
                    'orderId' => $order->code,
                    'orderNumber' => $order->code,
                    'cancellationStatus' => 'approved',
                    'estimatedRefundAmount' => $order->grand_total,
                    'estimatedRefundDate' => now()->addDays(3)->toDateString()
                ],
                'message' => 'Order cancelled successfully'
            ]);

        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to cancel order',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Checks if an order is eligible for cancellation
     *
     * @param string $orderId Order ID to check
     * @return JsonResponse Response with cancellable status and reason
     */
    public function isOrderCancellable(string $orderId): JsonResponse
    {
        try {
            // Get the authenticated user
            $user = auth()->user();

            if (!$user) {
                return $this->error(
                    'UNAUTHORIZED',
                    'Authentication required',
                    null,
                    401
                );
            }

            // Get the order
            $order = Order::where('code', $orderId)
                ->where('user_id', $user->id)
                ->first();

            if (!$order) {
                return $this->error(
                    'NOT_FOUND',
                    'Order not found or does not belong to the authenticated user',
                    null,
                    404
                );
            }

            // Check if the order has already been cancelled
            $orderCancel = OrderCancel::where('order_id', $order->id)->first();
            if ($orderCancel) {
                return $this->success([
                    'cancellable' => false,
                    'reason' => 'You have already requested to cancel this order'
                ]);
            }

            // Check if the order is in a cancellable state
            $cancellable = false;
            $reason = '';

            // Orders can typically be cancelled if they are in pending or processing status
            // and within a certain timeframe (e.g., within 24 hours of placing the order)
            if ($order->delivery_status === 'pending' || $order->delivery_status === 'processing') {
                $orderTime = Carbon::parse($order->created_at);
                $currentTime = Carbon::now();
                $hoursDifference = $currentTime->diffInHours($orderTime);

                if ($hoursDifference <= 24) {
                    $cancellable = true;
                } else {
                    $reason = 'Orders can only be cancelled within 24 hours of being placed';
                }
            } else if ($order->delivery_status === 'shipped') {
                $reason = 'Orders that have been shipped cannot be cancelled, but may be eligible for return upon delivery';
            } else if ($order->delivery_status === 'delivered') {
                $reason = 'Delivered orders cannot be cancelled, but may be eligible for return';
            } else if ($order->delivery_status === 'cancelled') {
                $reason = 'This order has already been cancelled';
            } else if ($order->delivery_status === 'returned') {
                $reason = 'This order has already been returned';
            }

            return $this->success([
                'cancellable' => $cancellable,
                'reason' => $cancellable ? null : $reason
            ]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to check if order is cancellable',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get list of available cancellation reasons
     *
     * @return JsonResponse Response with cancellation reasons
     */
    public function getCancellationReasons(): JsonResponse
    {
        try {
            // Common cancellation reasons
            $reasons = [
                [
                    'id' => 'changed_mind',
                    'reason' => 'Changed my mind',
                    'description' => 'I no longer want the product'
                ],
                [
                    'id' => 'found_better_price',
                    'reason' => 'Found a better price elsewhere',
                    'description' => 'I found the same product for a lower price'
                ],
                [
                    'id' => 'delivery_too_slow',
                    'reason' => 'Delivery taking too long',
                    'description' => 'The estimated delivery time is longer than expected'
                ],
                [
                    'id' => 'product_not_needed',
                    'reason' => 'Product no longer needed',
                    'description' => 'I no longer need the product'
                ],
                [
                    'id' => 'ordered_by_mistake',
                    'reason' => 'Ordered by mistake',
                    'description' => 'I ordered the wrong product or quantity'
                ],
                [
                    'id' => 'other',
                    'reason' => 'Other',
                    'description' => 'Other reason (please specify in additional comments)'
                ],
            ];

            return $this->success($reasons);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch cancellation reasons',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Adds all items from a previous order to the cart
     *
     * @param string $orderId
     * @return JsonResponse
     */
    public function reorderItems(string $orderId): JsonResponse
    {
        try {
            // Get the authenticated user
            $user = Auth::user();

            if (!$user) {
                return $this->error(
                    'UNAUTHORIZED',
                    'Authentication required',
                    null,
                    401
                );
            }

            // Get the order with all necessary relationships
            $order = Order::where('code', $orderId)
                ->where('user_id', $user->id)
                ->with(['orderDetails.product'])
                ->firstOrFail();

            // Clear existing cart items
            Cart::where('user_id', $user->id)->delete();
            CartInfo::where('user_id', $user->id)->delete();

            // Add order items to cart
            $cartItems = [];
            $cartTotal = 0;
            $cartTax = 0;
            $cartShipping = 0;
                $cartInfo = new CartInfo();
                $cartInfo->user_id = $user->id;
                $cartInfo->address_id = null; // User will need to select address
                $cartInfo->save();
            foreach ($order->orderDetails as $orderDetail) {

                $cart = new Cart();
                $cart->user_id = $user->id;
                $cart->cart_info_id = $cartInfo->id;
                $cart->product_id = $orderDetail->product_id;
                $cart->owner_id = $orderDetail->user_id;
                $cart->variation = $orderDetail->variation;
                $cart->product_referral_code = null;
                $cart->quantity = $orderDetail->quantity;
                $cart->price = $orderDetail->product->unit_price;
                $cart->tax = $orderDetail->tax ;
                $cart->shipping_cost = $orderDetail->shipping_cost ;
                $cart->save();
                $cartItems[] = $orderDetail;
                $cartTotal += $cart->price * $cart->quantity;
                $cartTax += $cart->tax;
                $cartShipping += $cart->shipping_cost;
            }
            return $this->success([
                'message' => 'Items from order #' . $order->code . ' have been added to your cart',
                'cart_count' => count($cartItems),
                'cart_total' => $cartTotal,
                'cart_tax' => $cartTax,
                'cart_shipping' => $cartShipping,
                'grand_total' => $cartTotal + $cartTax + $cartShipping
            ]);

        } catch (ModelNotFoundException $e) {
            return $this->error(
                'NOT_FOUND',
                'Order not found or does not belong to the authenticated user',
                null,
                404
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to reorder items: ' . $e->getMessage(),
                $e->getTraceAsString(),
                500
            );
        }
    }

    /**
     * Get returnable items from a specific order
     *
     * @param string $orderId
     * @return JsonResponse
     */
    public function getReturnableItems(string $orderId): JsonResponse
    {
        try {
            // Get the authenticated user
            $user = Auth::user();
            return $this->success($user);
            if (!$user) {
                return $this->error(
                    'UNAUTHORIZED',
                    'Authentication required',
                    null,
                    401
                );
            }

            // Get the order with all necessary relationships
            $order = Order::where('code', $orderId)
                ->where('user_id', $user->id)
                ->with(['orderDetails.product', 'user'])
                ->firstOrFail();

            // Check if the order is delivered (only delivered orders can have returnable items)
            if ($order->delivery_status != 'delivered') {
                return $this->error(
                    'INVALID_STATE',
                    'Only delivered orders can have returnable items',
                    null,
                    422
                );
            }

            // Check if the order is within the return period (e.g., 14 days)
            $returnPeriodDays = 14; // This could be a configurable setting
            $orderDeliveryDate = $order->updated_at; // Assuming updated_at is when the order was marked as delivered
            $returnDeadline = strtotime("+{$returnPeriodDays} days", strtotime($orderDeliveryDate));

            /*if (time() > $returnDeadline) {
                return $this->error(
                    'INVALID_STATE',
                    'Return period has expired for this order',
                    null,
                    422
                );
            }*/

            // Return the order with returnable flags
            return $this->success(
                new OrderResource($order, true) // Pass true to indicate we want returnable flags
            );

        } catch (ModelNotFoundException $e) {
            return $this->error(
                'NOT_FOUND',
                'Order not found or does not belong to the authenticated user',
                null,
                404
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to retrieve returnable items: ' . $e->getMessage(),
                $e->getTraceAsString(),
                500
            );
        }
    }
    public function trackOrder(string $trackingCode)
    {
        try {
            // Try to find order by code or tracking number
            $order = Order::with([
                'orderDetails.product', 
                'user',
                'carrier',
                'activityLogs' => function($query) {
                    $query->orderBy('created_at', 'asc');
                }
            ])
            ->where(function($query) use ($trackingCode) {
                $query->where('code', $trackingCode)
                      ->orWhere('tracking_number', $trackingCode);
            })
            ->first();
            
            if (!$order) {
                return $this->error('Order not found. Please check your order ID or tracking number and try again.', 404);
            }
            
            // Check if user is authenticated and if the order belongs to them
            // If not authenticated or order not belonging to user, only return limited information
            if (!auth()->check() || (auth()->check() && auth()->id() != $order->user_id && !auth()->user()->isAdmin())) {
                // Public tracking view with limited information
                return $this->success([
                    'code' => $order->code,
                    'delivery_status' => $order->delivery_status,
                    'created_at' => $order->created_at,
                    'tracking_number' => $order->tracking_number,
                    'tracking_url' => $order->tracking_url,
                    'carrier' => $order->carrier ? [
                        'name' => $order->carrier->name,
                        'logo' => $order->carrier->logo,
                    ] : null,
                    'activityLogs' => $order->activityLogs->map(function($log) {
                        return [
                            'description' => $log->description,
                            'created_at' => $log->created_at,
                        ];
                    }),
                    'is_public_view' => true
                ]);
            }
            
            // Get shipping milestones with timestamps
            $milestones = [];
            $statuses = [
                'ORDER_PLACED' => null,
                'CONFIRMED' => null,
                'PICKED_UP' => null,
                'ON_THE_WAY' => null,
                'DELIVERED' => null
            ];
            
            foreach ($order->activityLogs as $log) {
                if (strpos($log->description, 'placed') !== false) {
                    $statuses['ORDER_PLACED'] = $log->created_at;
                }
                if (strpos($log->description, 'confirmed') !== false) {
                    $statuses['CONFIRMED'] = $log->created_at;
                }
                if (strpos($log->description, 'picked up') !== false) {
                    $statuses['PICKED_UP'] = $log->created_at;
                }
                if (strpos($log->description, 'on the way') !== false || strpos($log->description, 'shipped') !== false) {
                    $statuses['ON_THE_WAY'] = $log->created_at;
                }
                if (strpos($log->description, 'delivered') !== false) {
                    $statuses['DELIVERED'] = $log->created_at;
                }
            }
            
            // Fill in milestones with available data
            foreach ($statuses as $status => $timestamp) {
                $milestones[] = [
                    'status' => $status,
                    'timestamp' => $timestamp,
                    'completed' => $timestamp !== null
                ];
            }
            
            // Add ETA if package is in transit
            $eta = null;
            if ($order->delivery_status === 'ON_THE_WAY' && !empty($statuses['ON_THE_WAY'])) {
                // Add 3-5 business days from shipping date as estimate
                $shippedDate = Carbon::parse($statuses['ON_THE_WAY']);
                $eta = $shippedDate->addWeekdays(rand(3, 5))->format('Y-m-d');
            }
            
            // Add seller information if available
            $sellerInfo = null;
            if ($order->seller_id) {
                $seller = User::find($order->seller_id);
                if ($seller) {
                    $sellerInfo = [
                        'id' => $seller->id,
                        'name' => $seller->name,
                        'email' => $seller->email,
                        'avatar' => $seller->avatar ? uploaded_asset($seller->avatar) : null,
                    ];
                }
            }
            
            // Prepare full response with enhanced data
            $responseData = new OrderResource($order);
            $responseData->additional([
                'milestones' => $milestones,
                'eta' => $eta,
                'seller' => $sellerInfo,
                'is_public_view' => false
            ]);

            return $this->success($responseData);

        } catch (QueryException $e) {
            Log::channel('api_order')->error('Database error while tracking order', [
                'message' => $e->getMessage(),
                'orderId' => $orderId
            ]);
            return $this->error('A database error occurred. Please try again later.', 500);
        } catch (ModelNotFoundException $e) {
            Log::channel('api_order')->error('Order not found while tracking', [
                'message' => $e->getMessage(),
                'orderId' => $orderId
            ]);
            return $this->error('Requested resource not found.', 404);
        } catch (\Exception $e) {
            Log::channel('api_order')->error('Unexpected error while tracking order', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'orderId' => $orderId
            ]);
            return $this->error('Something went wrong. Please try again later.', 500);
        }
    }

    /**
     * Get order statistics
     * 
     * Returns statistics about orders grouped by their status
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOrderStats(Request $request): JsonResponse
    {
        try {
            // Get user ID for filtering orders - use authenticated user if no specific user_id provided
            $userId = $request->user_id ?? auth()->id();

            // Define the statuses we want to count
            $statuses = [
                'processing' => [OrderStatus::ORDER_PLACED, OrderStatus::CONFIRMED, OrderStatus::PICKED_UP],
                'shipped' => [OrderStatus::ON_THE_WAY],
                'delivered' => [OrderStatus::DELIVERED],
                'cancelled' => [OrderStatus::CANCELLED],
                'returned' => [OrderStatus::RETURNED_DELIVERY, OrderStatus::RETURN],
            ];

            // Start with empty counts
            $stats = [
                'total' => 0,
                'processing' => 0,
                'shipped' => 0,
                'delivered' => 0,
                'cancelled' => 0,
                'returned' => 0,
            ];

            // Query to get counts by delivery status
            $query = Order::query();
            
            // Filter by user if not an admin
            if (!auth()->user()->isAdmin()) {
                $query->where('user_id', $userId);
            }

            // Get total count
            $stats['total'] = $query->count();

            // Get counts for each status group
            foreach ($statuses as $groupName => $statusValues) {
                $count = Order::where(function($query) use ($statusValues) {
                    $query->whereIn('delivery_status', $statusValues);
                });
                
                // Filter by user if not an admin
                if (!auth()->user()->isAdmin()) {
                    $count->where('user_id', $userId);
                }
                
                $stats[$groupName] = $count->count();
            }

            // Log the successful request
            Log::channel('api_order')->info('Order statistics fetched successfully');

            return $this->success($stats);
        } catch (\Exception $e) {
            // Log the error for debugging
            Log::channel('api_order')->error('Error fetching order statistics', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return $this->error('Something went wrong while fetching order statistics', 500);
        }
    }

    /**
     * Get details of a specific dropshipper order
     *
     * @param Request $request
     * @param string $orderId
     * @return JsonResponse
     */
    public function get_dropshipper_order_details(Request $request, $orderId): JsonResponse
    {
        $user = auth()->user();
        
        try {
            // Find the order, ensuring it belongs to the authenticated user
            $order = DB::table('bulk_orders')
                ->where('id', $orderId)
                ->where('user_id', $user->id)
                ->first();
                
            if (!$order) {
                return $this->error(
                    'Order Not Found',
                    'The requested order could not be found or does not belong to you',
                    null,
                    404
                );
            }
            
            // Get order items
            $orderItems = DB::table('bulk_order_items')
                ->where('bulk_order_id', $orderId)
                ->get();
                
            $items = [];
            foreach ($orderItems as $item) {
                $product = Product::find($item->product_id);
                if ($product) {
                    $items[] = [
                        'id' => $item->id,
                        'productId' => $item->product_id,
                        'name' => $product->name,
                        'slug' => $product->slug,
                        'price' => $item->price,
                        'originalPrice' => $product->unit_price,
                        'image' => uploaded_asset($product->thumbnail_img),
                        'quantity' => $item->quantity,
                        'notes' => $item->notes,
                        'sku' => $product->sku,
                        'variation' => $item->variation,
                    ];
                }
            }
            
            // Get shipping address
            $shippingAddress = DB::table('addresses')
                ->where('id', $order->shipping_address_id)
                ->first();
                
            // Format the response
            $response = [
                'id' => $order->id,
                'orderId' => $order->id,
                'orderNumber' => $order->order_number,
                'status' => $order->status,
                'date' => $order->created_at,
                'total' => $order->total,
                'itemCount' => count($items),
                'items' => $items,
                'shippingAddress' => $shippingAddress ? [
                    'firstName' => $shippingAddress->first_name,
                    'lastName' => $shippingAddress->last_name,
                    'addressLine1' => $shippingAddress->address,
                    'addressLine2' => $shippingAddress->address2,
                    'city' => $shippingAddress->city,
                    'state' => $shippingAddress->state,
                    'postalCode' => $shippingAddress->postal_code,
                    'country' => $shippingAddress->country,
                    'phoneNumber' => $shippingAddress->phone,
                ] : null,
            ];
            
            // Add tracking information if available
            if ($order->tracking_number) {
                $response['trackingInfo'] = [
                    'carrier' => $order->carrier ?? 'Standard Shipping',
                    'trackingNumber' => $order->tracking_number,
                    'trackingUrl' => $order->tracking_url,
                ];
            }
            
            return $this->success(
                $response,
                'Order details retrieved successfully',
                200
            );
            
        } catch (\Exception $e) {
            Log::channel('api_order')->error('Error retrieving dropshipper order details: ' . $e->getMessage());
            return $this->error(
                'Server Error',
                'Failed to retrieve order details: ' . $e->getMessage(),
                null,
                500
            );
        }
    }

    /**
     * Place a bulk purchase request order from the product page form
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function placeBulkPurchaseRequest(Request $request)
    {
        try {
            $user = auth()->user();
            
            // Validate input
            $validator = Validator::make($request->all(), [
                'items' => 'required|array|min:1',
                'items.*.productId' => 'required|exists:products,id',
                'items.*.quantity' => 'required|integer|min:1',
                'shippingAddress' => 'required',
                'shippingAddress.firstName' => 'required|string',
                'shippingAddress.lastName' => 'string',
                'shippingAddress.phoneNumber' => 'required|string',
                'shippingAddress.email' => 'required|email',
                'paymentMethod' => 'required|string',
                'additionalInfo' => 'array',
            ]);
            
            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    $validator->errors()->first(),
                    422
                );
            }
            
            // Process bulk order
            DB::beginTransaction();
            
            try {
                // Create or get shipping address
                $address = Address::firstOrCreate(
                    [
                        'user_id' => $user->id,
                        'phone' => $request->input('shippingAddress.phoneNumber'),
                        'email' => $request->input('shippingAddress.email')
                    ],
                    [
                        'name' => $request->input('shippingAddress.firstName') . ' ' . $request->input('shippingAddress.lastName'),
                        'address' => $request->input('shippingAddress.addressLine1', ''),
                        'country' => $request->input('shippingAddress.country', ''),
                        'city' => $request->input('shippingAddress.city', ''),
                        'postal_code' => $request->input('shippingAddress.postalCode', ''),
                        'state' => $request->input('shippingAddress.state', ''),
                    ]
                );
                
                // Format shipping address for order
                $shipping_address = $this->apiOrderService->formatShippingAddress($address);
                
                // Create combined order
                $combined_order = $this->apiOrderService->createCombinedOrder($user->id, $shipping_address);
                $combined_order_code = $combined_order->combined_order_code;
                
                // Create the order using ApiOrderService for consistency
                $order = $this->apiOrderService->createOrder(
                    $combined_order_code,
                    $combined_order->id,
                    $user->id,
                    $shipping_address,
                    $request->input('additionalInfo') ? json_encode($request->input('additionalInfo')) : null,
                    0, // is_gift
                    'bulk_order_api',
                    null // seller will be set in order details
                );
                $order->is_bulk_order = 1;
                $order->payment_type = $request->input('paymentMethod');
                $order->notes = $request->input('notes', '');
                $order->save();
                
                // Calculate order totals
                $subtotal = 0;
                $tax = 0;
                $shipping = 100; // Default shipping cost
                
                // Process items and create order details
                foreach ($request->input('items') as $item) {
                    $product = Product::findOrFail($item['productId']);
                    $quantity = $item['quantity'];
                    
                    // Create order detail
                    $order_detail = new OrderDetail();
                    $order_detail->product_id = $product->id;
                    $order_detail->seller_id = $product->user_id;
                    $order_detail->quantity = $quantity;
                    $order_detail->price = $product->unit_price;
                    
                    // Apply bulk discount if applicable
                    if ($product->bulk_discount_percentage > 0) {
                        $discounted_price = $product->unit_price * (1 - ($product->bulk_discount_percentage / 100));
                        $order_detail->price = $discounted_price;
                    }
                    
                    $itemTotal = $order_detail->price * $quantity;
                    $subtotal += $itemTotal;
                    
                    // Set other details
                    if ($product->variations) {
                        $order_detail->variation = "";
                        $order_detail->unit_price = $product->unit_price;
                    }
                    
                    if ($item['notes'] ?? false) {
                        $order_detail->note = $item['notes'];
                    }
                    
                    // Save after order is created
                    $order_details[] = $order_detail;
                }
                
                // Set order totals
                $order->grand_total = $subtotal + $tax + $shipping;
                $order->coupon_discount = 0;
                $order->shipping_cost = $shipping;
                $order->tax = $tax;
                $order->save();
                
                // Save order details
                foreach ($order_details as $detail) {
                    $detail->order_id = $order->id;
                    $detail->save();
                }
                
                // Update combined order total
                $combined_order->grand_total = $order->grand_total;
                $combined_order->save();
                
                // Commit transaction
                DB::commit();
                
                // Send notification to admin about new order
                NotificationHelper::orderCreated($combined_order);
                
                // Prepare response
                $response = [
                    'orderId' => $order->id,
                    'code' => $order->code,
                    'success' => true,
                    'message' => 'Bulk purchase request submitted successfully'
                ];
                
                return $this->success(
                    $response,
                    'Bulk purchase request submitted successfully',
                    200
                );
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Error processing bulk purchase request: ' . $e->getMessage());
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('Failed to place bulk purchase request: ' . $e->getMessage());
            return $this->error(
                'INTERNAL_SERVER_ERROR',
                'Failed to process your request: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * Save bulk order from the checkout process
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveBulkOrder(Request $request)
    {
        try {
            $user = auth()->user();
            
            // Validate input
            $validator = Validator::make($request->all(), [
                'items' => 'required|array|min:1',
                'items.*.productId' => 'required|exists:products,id',
                'items.*.quantity' => 'required|integer|min:1',
                'shippingAddress' => 'required|array',
                'shippingMethod' => 'required|array',
                'totalAmount' => 'required|numeric|min:0',
            ]);
            
            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    $validator->errors()->first(),
                    422
                );
            }
            
            // Process bulk order
            DB::beginTransaction();
            
            try {
                // Create or get shipping address
                $address = Address::firstOrCreate(
                    [
                        'user_id' => $user->id,
                        'phone' => $request->input('shippingAddress.phone', ''),
                        'email' => $request->input('shippingAddress.email', $user->email),
                    ],
                    [
                        'name' => $request->input('shippingAddress.name'),
                        'address' => $request->input('shippingAddress.street'),
                        'country' => $request->input('shippingAddress.country'),
                        'city' => $request->input('shippingAddress.city'),
                        'postal_code' => $request->input('shippingAddress.zip'),
                        'state' => $request->input('shippingAddress.state'),
                    ]
                );
                
                // Format shipping address for order
                $shipping_address = $this->apiOrderService->formatShippingAddress($address);
                
                // Create combined order
                $combined_order = $this->apiOrderService->createCombinedOrder($user->id, $shipping_address);
                $combined_order_code = $combined_order->combined_order_code;
                
                // Create the order using ApiOrderService for consistency
                $order = $this->apiOrderService->createOrder(
                    $combined_order_code,
                    $combined_order->id,
                    $user->id,
                    $shipping_address,
                    null, // additional_info
                    0, // is_gift
                    'bulk_order_checkout',
                    null // seller will be set in order details
                );
                $order->is_bulk_order = 1;
                $order->payment_type = 'awaiting_payment';
                $order->notes = $request->input('note', '');
                $order->save();
                
                // Calculate order totals
                $subtotal = 0;
                $tax = 0;
                $shipping = $request->input('shippingMethod.price', 0);
                
                // Process items and create order details
                foreach ($request->input('items') as $item) {
                    $product = Product::findOrFail($item['productId']);
                    $quantity = $item['quantity'];
                    
                    // Create order detail
                    $order_detail = new OrderDetail();
                    $order_detail->product_id = $product->id;
                    $order_detail->seller_id = $product->user_id;
                    $order_detail->quantity = $quantity;
                    $order_detail->price = $product->unit_price;
                    
                    // Apply bulk discount if applicable
                    if ($product->bulk_discount_percentage > 0) {
                        $discounted_price = $product->unit_price * (1 - ($product->bulk_discount_percentage / 100));
                        $order_detail->price = $discounted_price;
                    }
                    
                    $itemTotal = $order_detail->price * $quantity;
                    $subtotal += $itemTotal;
                    
                    // Set other details
                    if ($product->variations) {
                        $order_detail->variation = "";
                        $order_detail->unit_price = $product->unit_price;
                    }
                    
                    if (isset($item['notes'])) {
                        $order_detail->note = $item['notes'];
                    }
                    
                    // Save after order is created
                    $order_details[] = $order_detail;
                }
                
                // Set order totals
                $order->grand_total = $subtotal + $tax + $shipping;
                $order->coupon_discount = 0;
                $order->shipping_cost = $shipping;
                $order->tax = $tax;
                $order->save();
                
                // Save order details
                foreach ($order_details as $detail) {
                    $detail->order_id = $order->id;
                    $detail->save();
                }
                
                // Update combined order total
                $combined_order->grand_total = $order->grand_total;
                $combined_order->save();
                
                // Commit transaction
                DB::commit();
                
                // Send notification to admin about new order
                NotificationHelper::orderCreated($combined_order);
                
                // Prepare response
                $response = [
                    'orderId' => $order->id,
                    'code' => $order->code,
                    'success' => true,
                    'message' => 'Bulk order saved successfully'
                ];
                
                return $this->success(
                    $response,
                    'Bulk order saved successfully',
                    200
                );
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Error saving bulk order: ' . $e->getMessage());
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('Failed to save bulk order: ' . $e->getMessage());
            return $this->error(
                'INTERNAL_SERVER_ERROR',
                'Failed to save bulk order: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * Process order directly from cart with payment
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function process_order_from_cart(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'address_id' => 'required|string',
                'payment_method_id' => 'required|string',
                'additional_info' => 'nullable|string',
                'is_gift' => 'nullable|boolean',
                'order_from' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated'
                ], 401);
            }

            $additionalInfo = [
                'additional_info' => $request->additional_info,
                'is_gift' => $request->is_gift ?? false,
                'order_from' => $request->order_from ?? 'api'
            ];

            $result = $this->orderProcessingService->processOrderFromCart(
                $user,
                $request->address_id,
                $request->payment_method_id,
                $additionalInfo
            );

            if (!$result['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                    'error_code' => $result['error_code']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'data' => $result['data']
            ], 200);

        } catch (\Exception $e) {
            Log::channel('api_order')->error('Error processing order from cart: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Something went wrong while processing your order'
            ], 500);
        }
    }

    /**
     * Complete order after payment verification
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function complete_order(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'payment_intent_id' => 'required|string',
                'combined_order_id' => 'required|string',
                'payment_method' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated'
                ], 401);
            }

            $result = $this->orderProcessingService->completeOrderAfterPayment(
                $request->payment_intent_id,
                $request->combined_order_id,
                $request->payment_method,
                $user
            );

            if (!$result['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                    'error_code' => $result['error_code']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'data' => $result['data']
            ], 200);

        } catch (\Exception $e) {
            Log::channel('api_order')->error('Error completing order: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Something went wrong while completing your order'
            ], 500);
        }
    }

    /**
     * Get order details for thank you page
     *
     * @param string $orderCode
     * @return JsonResponse
     */
    public function getOrderForThankYou(string $orderCode): JsonResponse
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated'
                ], 401);
            }

            $order = Order::with([
                'orderDetails.product', 
                'user',
                'carrier',
                'activityLogs'
            ])
            ->where('code', $orderCode)
            ->where('user_id', $user->id)
            ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => new OrderResource($order)
            ], 200);

        } catch (\Exception $e) {
            Log::channel('api_order')->error('Error fetching order for thank you page: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'order_code' => $orderCode,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Something went wrong while fetching order details'
            ], 500);
        }
    }

    /**
     * Get user's recent orders
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getOrders(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'nullable|integer|min:1|max:50',
            ]);
            
            if ($validator->fails()) {
                return $this->validation_error(
                    'VALIDATION_ERROR',
                    'Please provide valid information',
                    $validator->errors()->messages(),
                    400
                );
            }
            
            $limit = $request->input('limit', 10);
            
            // Check if user is authenticated
            if (Auth::check()) {
                $user_id = Auth::user()->id;
                
                $orders = Order::with(['orderDetails', 'orderDetails.product'])
                    ->where('user_id', $user_id)
                    ->orderBy('created_at', 'desc')
                    ->limit($limit)
                    ->get();
                    
                return $this->success($orders);
            } else {
                // If not authenticated, return empty array with message
                return $this->success(
                    [], 
                    'Please login to view your orders'
                );
            }
        } catch (QueryException $e) {
            Log::channel('api_order')->error('Database error while fetching orders: ApiOrdersController', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->error('A database error occurred. Please try again later.', 500);
        } catch (\Exception $e) {
            Log::channel('api_order')->error('Unexpected error while fetching orders: ApiOrdersController', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return $this->error('Something went wrong. Please try again later.', 500);
        }
    }

    /**
     * Process payment for bulk order
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function processBulkOrderPayment(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'combined_order_id' => 'required|string',
                'payment_method_id' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated'
                ], 401);
            }

            $result = $this->orderProcessingService->processBulkOrderPayment(
                $request->combined_order_id,
                $request->payment_method_id,
                $user
            );

            if (!$result['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                    'error_code' => $result['error_code']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'data' => $result['data']
            ], 200);

        } catch (\Exception $e) {
            Log::channel('api_order')->error('Error processing bulk order payment: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Something went wrong while processing your payment'
            ], 500);
        }
    }

    /**
     * Complete bulk order after payment verification
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function completeBulkOrder(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'payment_intent_id' => 'required|string',
                'combined_order_id' => 'required|string',
                'payment_method' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated'
                ], 401);
            }

            $result = $this->orderProcessingService->completeOrderAfterPayment(
                $request->payment_intent_id,
                $request->combined_order_id,
                $request->payment_method,
                $user
            );

            if (!$result['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                    'error_code' => $result['error_code']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'data' => $result['data']
            ], 200);

        } catch (\Exception $e) {
            Log::channel('api_order')->error('Error completing bulk order: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Something went wrong while completing your order'
            ], 500);
        }
    }

}


