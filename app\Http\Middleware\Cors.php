<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class Cors
{
    public function handle(Request $request, Closure $next)
    {
        // <PERSON>le preflight OPTIONS request
        if ($request->getMethod() === "OPTIONS") {
            return response()->json(['message' => 'CORS preflight'], 200, $this->getCorsHeaders($request));
        }

        try {
            $response = $next($request);
        } catch (\Exception $e) {
            \Log::error('CORS Middleware Exception', [
                'path' => $request->path(),
                'error' => $e->getMessage()
            ]);

            $response = response()->json([
                'status' => 'error',
                'message' => 'Internal server error',
                'error' => $e->getMessage()
            ], 500);
        }

        // Add CORS headers
        if ($response && $response->headers) {
            foreach ($this->getCorsHeaders($request) as $key => $value) {
                $response->headers->set($key, $value);
            }
        }

        // Add full Content Security Policy header
       /* $csp = "
            default-src 'self';
            script-src 'self' 'unsafe-inline' 'unsafe-eval' https://analytics.buzfi.com https://js.stripe.com https://*.stripe.com https://static.cloudflareinsights.com https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js https://cdn.jsdelivr.net/npm/sortablejs@1.14.0/Sortable.min.js https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css https://fonts.googleapis.com';
            style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
            img-src 'self' data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com;
            font-src 'self' https://fonts.gstatic.com;
            media-src 'self' data: blob: https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com;
            connect-src 'self' https://api.iamnahid.me https://api.buzfi.com https://buzfi.nyc3.digitaloceanspaces.com https://buzfi.nyc3.cdn.digitaloceanspaces.com;
            frame-src https://js.stripe.com https://*.stripe.com;
            object-src 'none';
        ";*/
        $csp = "
    default-src 'self';
    script-src 'self' 'unsafe-inline' 'unsafe-eval'
        https://analytics.buzfi.com
        https://js.stripe.com
        https://*.stripe.com
        https://static.cloudflareinsights.com
        https://cdnjs.cloudflare.com
        https://cdn.jsdelivr.net;

    style-src 'self' 'unsafe-inline'
        https://fonts.googleapis.com
        https://cdnjs.cloudflare.com
        https://cdn.jsdelivr.net;

    img-src 'self' data: blob:
        https://buzfi.nyc3.digitaloceanspaces.com
        https://buzfi.nyc3.cdn.digitaloceanspaces.com;

    font-src 'self' https://fonts.gstatic.com;

    media-src 'self' data: blob:
        https://buzfi.nyc3.digitaloceanspaces.com
        https://buzfi.nyc3.cdn.digitaloceanspaces.com;

    connect-src 'self'
        https://api.iamnahid.me
        https://api.buzfi.com
        https://buzfi.nyc3.digitaloceanspaces.com
        https://buzfi.nyc3.cdn.digitaloceanspaces.com;

    frame-src https://js.stripe.com https://*.stripe.com;

    object-src 'none';
";


        $response->headers->set(
            'Content-Security-Policy',
            preg_replace('/\s{2,}/', ' ', trim($csp))
        );

        return $response;
    }

    private function getCorsHeaders(Request $request): array
    {
        $origin = $this->getAllowedOrigin($request);

        return [
            'Access-Control-Allow-Origin' => $origin,
            'Access-Control-Allow-Methods' => 'GET, POST, PUT, DELETE, OPTIONS, PATCH',
            'Access-Control-Allow-Headers' => 'Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN, Accept, Origin, X-Api-Key, X-Cart-Id, Cache-Control, Pragma, Expires, X-Temp-User-Id, X-Request-ID',
            'Access-Control-Allow-Credentials' => 'true',
            'Access-Control-Max-Age' => '86400',
            'Access-Control-Expose-Headers' => 'Authorization, X-Pagination-Count, X-Pagination-Page, X-Pagination-Limit',
        ];
    }

    private function getAllowedOrigin(Request $request)
    {
        $allowedOrigins = config('cors.allowed_origins', [
            'http://localhost:3000',
            'https://localhost:3000',
            'http://localhost/buzfi-main/buzfi',
            'https://buzfi.com',
            'https://www.buzfi.com',
            'http://buzfi.com',
            'http://www.buzfi.com',
            'http://127.0.0.1:3000',
            'https://127.0.0.1:3000',
            'https://api.iamnahid.me',
            'https://buzfi.nyc3.digitaloceanspaces.com',
            'https://buzfi.nyc3.cdn.digitaloceanspaces.com',
            'https://iamnahid.me/',
            'https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js',
            'https://cdn.jsdelivr.net/npm/sortablejs@1.14.0/Sortable.min.js',
            'https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css',
            'https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css'
        ]);

        $origin = $request->headers->get('Origin');

        if (!$origin) {
            return $allowedOrigins[0] ?? 'http://localhost:3000';
        }

        if (in_array($origin, $allowedOrigins)) {
            return $origin;
        }

        // Allow localhost in development
        if (config('app.env') === 'development' && preg_match('/^https?:\/\/(localhost|127\.0\.0\.1)(:\d+)?$/', $origin)) {
            return $origin;
        }

        return $allowedOrigins[0] ?? 'http://localhost:3000';
    }
}
