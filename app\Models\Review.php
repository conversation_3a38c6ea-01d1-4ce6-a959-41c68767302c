<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Review extends Model
{
    protected $fillable = [
        'product_id',
        'review_title',
        'user_id',
        'rating',
        'comment',
        'photos',
        'review_videos',
        'seller_response',
        'seller_response_date',
        'dropshipper_response',
        'dropshipper_response_date',
        'helpful_count',
        'tags',
        'verified',
        'status',
        'viewed'
    ];

    protected $casts = [
        'photos' => 'array',
        'review_videos' => 'array',
        'tags' => 'array',
        'verified' => 'boolean',
        'viewed' => 'boolean',
        'seller_response_date' => 'datetime',
        'dropshipper_response_date' => 'datetime'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
