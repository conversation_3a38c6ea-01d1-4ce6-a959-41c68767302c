<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class Order extends Model
{
    protected $fillable = [
        'payment_status',
        'delivery_status',
        'payment_details',
        'payment_charge_id',
        'pending_order_email',
        'cancel_pending_order_email',
        'pending_order_reminder_email',
        'delivery_viewed',
        'payment_status_viewed',
        'is_bulk_order',
        'additional_info',
        'notes'
    ];

    protected $casts = [
        'shipping_address' => 'array',
        'additional_info' => 'array',
        'is_bulk_order' => 'boolean'
    ];

    /**
     * The event map for the model.
     *
     * @var array
     */
    protected $dispatchesEvents = [
        'created' => \App\Events\OrderCreated::class,
        'updated' => \App\Events\OrderUpdated::class,
    ];

    public function orderDetails()
    {
        return $this->hasMany(OrderDetail::class);
    }

    public function refund_requests()
    {
        return $this->hasMany(RefundRequest::class);
    }

    public function return_requests()
    {
        return $this->hasMany(ReturnRequestInfo::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function shop()
    {
        return $this->hasOne(Shop::class, 'user_id', 'seller_id');
    }

    public function seller()
    {
        return $this->belongsTo(User::class, 'seller_id', 'id');
    }

    public function pickup_point()
    {
        return $this->belongsTo(PickupPoint::class);
    }

    public function carrier()
    {
        return $this->belongsTo(Carrier::class);
    }

    public function affiliate_log()
    {
        return $this->hasMany(AffiliateLog::class);
    }

    public function club_point()
    {
        return $this->hasMany(ClubPoint::class);
    }

    public function delivery_boy()
    {
        return $this->belongsTo(User::class, 'assign_delivery_boy', 'id');
    }

    public function proxy_cart_reference_id()
    {
        return $this->hasMany(ProxyPayment::class)->select('reference_id');
    }
    public function products()
    {
        return $this->hasManyThrough(Product::class, OrderDetail::class, 'order_id', 'id', 'id', 'product_id');
    }
    public function activityLogs()
    {
        return $this->morphMany(ActivityLog::class, 'subject')
            ->where('subject_type', 'App\Models\Order')->orderBy('created_at', 'asc');
    }

    /**
     * Get readable payment method details from payment details JSON
     *
     * @param string|null $payment_details Payment details JSON string
     * @param string|null $status Payment status
     * @return array|string Array of payment details or empty string on error
     */
    public function getReadablePaymentMethodDetails($payment_details, $status)
    {
        try {
            if (empty($payment_details)) {
                return '';
            }

            $details = json_decode($payment_details, true);

            if (!$details || !isset($details['card_details'])) {
                return '';
            }

            $card = json_decode($details['card_details'], true);

            if (!$card || !isset($card['brand']) || !isset($card['last4'])) {
                return '';
            }

            return [
                'method' => ucfirst($card['brand']),
                'last4' => $card['last4'],
                'status' => ucfirst($status),
            ];

        } catch (\Exception $e) {
            \Log::error('Error parsing payment details: ' . $e->getMessage());
            return '';
        }
    }
    public function getBillingAddress($payment_details, $shipping_address): array
    {
        // Try to get billing address from payment details
        try {
            if ($payment_details) {
                $details = json_decode($payment_details, true);
                if (isset($details['card_details'])) {
                    $card = json_decode($details['card_details'], true);
                    if (isset($card['billing_address'])) {
                        $billingAddress = $card['billing_address'];
                        return [
                            'name' => $billingAddress['name'] ?? $this->user->name ?? '',
                            'street' => $billingAddress['line1'] ?? '',
                            'city' => $billingAddress['city'] ?? '',
                            'state' => $billingAddress['state'] ?? '',
                            'zip' => $billingAddress['postal_code'] ?? '',
                            'country' => $billingAddress['country'] ?? '',
                            'phone' => $billingAddress['phone'] ?? $this->user->phone ?? ''
                        ];
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('Error parsing billing address: ' . $e->getMessage());
        }

        // Fallback to shipping address if billing address not available
        $shippingAddress = json_decode($shipping_address);
        return [
            'name' => optional($shippingAddress)->name ?? $this->user->name ?? '',
            'street' => optional($shippingAddress)->address ?? '',
            'city' => optional($shippingAddress)->city ?? '',
            'state' => optional($shippingAddress)->state ?? '',
            'zip' => optional($shippingAddress)->postal_code ?? '',
            'country' => optional($shippingAddress)->country ?? '',
            'phone' => optional($shippingAddress)->phone ?? $this->user->phone ?? ''
        ];
    }
    public function getShippingAddress($shipping_address): array
    {
        $shippingAddress = json_decode($shipping_address);
        return [
            'fullName' => optional($shippingAddress)->receiver_name ?? $this->user->name ?? '',
            'streetAddress' => optional($shippingAddress)->street_address ?? '',
            'streetAddress2' => optional($shippingAddress)->apartment_address ?? '',
            'city' => optional($shippingAddress)->city ?? '',
            'state' => optional($shippingAddress)->state ?? '',
            'zipCode' => optional($shippingAddress)->postal_code ?? '',
            'country' => optional($shippingAddress)->country ?? '',
            'phone' => optional($shippingAddress)->phone ?? $this->user->phone ?? ''
        ];
    }

    /**
     * Get all items for this order
     */
    public function items()
    {
        return $this->hasMany(OrderItem::class);
    }
}
