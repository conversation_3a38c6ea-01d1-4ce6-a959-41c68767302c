<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Http\Resources\V2\CitiesCollection;
use App\Http\Resources\V2\CountriesCollection;
use App\Http\Resources\V2\StatesCollection;
use App\Http\Resources\V3\ShippingAddress\ShippingAddressesResource;
use App\Http\Resources\V3\ShippingAddress\ShippingAddressResource;
use App\Models\Address;
use App\Models\City;
use App\Models\Country;
use App\Models\State;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ApiAddressController extends ApiResponse
{
    public function getCountries(Request $request)
    {
        $country_query = Country::where('status', 1);
        if ($request->name != "" || $request->name != null) {
            $country_query->where('name', 'like', '%' . $request->name . '%');
        }
        $countries = $country_query->get();
        return $this->success(new CountriesCollection($countries), 'Countries List');
    }
    public function getStates(Request $request)
    {
        // Get country code from request
        $country = $request->route('country') ?? $request->input('country') ?? 'usa';

        // Find country by code
        $countryModel = Country::where('code', strtoupper($country))
                            ->orWhere('code', strtolower($country))
                            ->first();

        if (!$countryModel) {
            // Try with USA (common fallback)
            $countryModel = Country::where('code', 'USA')->first();

            // If still no match, get the first active country
            if (!$countryModel) {
                $countryModel = Country::where('status', 1)->first();

                // If still no country exists at all, return empty array
                if (!$countryModel) {
                    return $this->success([], 'No countries found in database');
                }
            }
        }

        // Get states for the country
        $states = State::where('status', 1)
                    ->where('country_id', $countryModel->id)
                    ->get();

        // Track used codes to prevent duplicates
        $usedCodes = [];

        // Transform for frontend format
        $transformedStates = $states->map(function($state) use ($countryModel, &$usedCodes) {
            // Get base code (use state name if code is null)
            $baseCode = $state->code ?? strtoupper(substr($state->name, 0, 2));

            // If this code already exists, make it unique by appending the ID
            $stateCode = $baseCode;
            if (in_array($stateCode, $usedCodes)) {
                $stateCode = $baseCode . '-' . $state->id;
            }

            // Add to used codes
            $usedCodes[] = $stateCode;

            return [
                'id' => (string)$state->id,
                'name' => $state->name,
                'code' => $stateCode,
                'country' => $state->country ? $state->country->code : $countryModel->code ?? 'usa'
            ];
        });

        return $this->success($transformedStates, 'States List');
    }
    public function getCities()
    {
        return $this->success(new CitiesCollection(City::where('status', 1)->get()));
    }
    public function getCitiesByState($state_id, Request $request)
    {
        // Validate state_id exists
        $state = State::where('id', $state_id)
                    ->first();

        if (!$state) {
            return $this->success([], 'State not found');
        }

        $city_query = City::where('status', 1)->where('state_id', $state->id);

        if ($request->name != "" || $request->name != null) {
            $city_query->where('name', 'like', '%' . $request->name . '%');
        }

        $cities = $city_query->get();
        return $this->success(new CitiesCollection($cities));
    }
    public function getStatesByCountry($country_id,Request $request)
    {
        $state_query = State::where('status', 1)->where('country_id',$country_id);
        if ($request->name != "" || $request->name != null) {
            $state_query->where('name', 'like', '%' . $request->name . '%');
        }
        $states = $state_query->get();
        return $this->success(new StatesCollection($states));
    }
    public function addCity(Request $request)
    {
        $messages = array(
            'state_id.required' => translate('State ID is required'),
            'state_id.exists' => translate('State ID does not exist'),
            'city_name.required' => translate('City Name is required'),
        );
        $validator = Validator::make($request->all(), [
            'state_id' => 'required|exists:states,id',
            'city_name' => 'required|string|max:255'
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }
        $city = new City();
        $city->state_id = $request->state_id;
        $city->name = $request->city_name;
        $city->save();
        return $this->success(new CitiesCollection($city));
    }

    public function createShippingAddress(Request $request){

        // Check if user is authenticated
        if (!auth()->check()) {
            return $this->error('User not authenticated', 401);
        }

        $messages = array(
            'fullName.required' => translate('Name is required'),
            'streetAddress.required' => translate('Street Address is required'),
            'apartment_address.required' => translate('Address is required'),
            'country_id.required' => translate('Country ID is required'),
            'country_id.exists' => translate('Country ID does not exist'),
            'state_id.required' => translate('State ID is required'),
            'state_id.exists' => translate('State ID does not exist'),
            'city_id.required' => translate('City ID required'),
            'city_id.exists' => translate('City ID does not exist'),
            'zipCode.required' => translate('Postal Code required'),
        );
        $validator = Validator::make($request->all(), [
            'fullName' => 'required',
            'streetAddress' => 'required',
            'streetAddress2' => 'nullable',
            'country_id' => 'required|exists:countries,id',
            'state_id' => 'required|exists:states,id',
            'city_id' => 'required|exists:cities,id',
            'zipCode' => 'required',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }
        if ($request->isDefault===  1) {
            $addresses = Address::where('user_id', auth()->user()->id)->get();
            foreach ($addresses as $key => $add) {
                $add->set_default = 0;
                $add->save();
            }
        }


        $address = new Address;
        if ($request->has('customer_id')) {
            $address->user_id   = $request->customer_id;
        } else {
            $address->user_id   = auth()->user()->id;
        }
        $address->receiver_name = $request->fullName;
        $address->street_address       = $request->streetAddress;
        $address->apartment_address       = $request->streetAddress2;
        $address->country_id    = $request->country_id;
        $address->state_id      = $request->state_id;
        $address->city_id       = $request->city_id;
        $address->longitude     = $request->longitude;
        $address->latitude      = $request->latitude;
        $address->postal_code   = $request->zipCode;
        $address->phone         = $request->phone;
        $address->set_default    = $request->isDefault;
        $address->save();

        return $this->success(new ShippingAddressResource($address));
    }

    public function updateShippingAddress(Request $request, $id)
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            return $this->error('User not authenticated', 401);
        }

        $address = Address::find($id);
        if (!$address) {
            return $this->error('Address not found', 404);
        }

        // $messages = array(
        //     'fullName.required' => translate('Name is required'),
        //     'streetAddress.required' => translate('Street Address is required'),
        //     'apartment_address.required' => translate('Address is required'),
        //     'country_id.required' => translate('Country ID is required'),
        //     'country_id.exists' => translate('Country ID does not exist'),
        //     'state_id.required' => translate('State ID is required'),
        //     'state_id.exists' => translate('State ID does not exist'),
        //     'city_id.required' => translate('City ID required'),
        //     'city_id.exists' => translate('City ID does not exist'),
        //     'zipCode.required' => translate('Postal Code required'),
        // );
        // $validator = Validator::make($request->all(), [
        //     'fullName' => 'required',
        //     'streetAddress' => 'required',
        //     'streetAddress2' => 'required',
        //     'country_id' => 'required|exists:countries,id',
        //     'state_id' => 'required|exists:states,id',
        //     'city_id' => 'required|exists:cities,id',
        //     'zipCode' => 'required',
        // ], $messages);

        // if ($validator->fails()) {
        //     return $this->validation_error('Validation Error', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        // }

        if ($request->isDefault===  1) {
            $addresses = Address::where('user_id', auth()->user()->id)->get();
            foreach ($addresses as $key => $add) {
                $add->set_default = 0;
                $add->save();
            }
        }


        if ($request->has('customer_id')) {
            $address->user_id   = $request->customer_id;
        } else {
            $address->user_id   = auth()->user()->id;
        }
        $address->receiver_name = $request->fullName ?? $address->receiver_name;
        $address->street_address       = $request->streetAddress ?? $address->street_address;
        $address->apartment_address       = $request->streetAddress2 ?? $address->apartment_address;
        $address->country_id    = $request->country_id ?? $address->country_id;
        $address->state_id      = $request->state_id ?? $address->state_id;
        $address->city_id       = $request->city_id ?? $address->city_id;
        $address->longitude     = $request->longitude ?? $address->longitude    ;
        $address->latitude      = $request->latitude ?? $address->latitude;
        $address->postal_code   = $request->zipCode ?? $address->postal_code;
        $address->phone         = $request->phone ?? $address->phone;
        $address->set_default    = $request->isDefault ?? $address->set_default;
        $address->save();

        return $this->success(new ShippingAddressResource($address));
    }
    public function shipping_address_list()
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            return $this->error('User not authenticated', 401);
        }

       return $this->success(new ShippingAddressesResource(Address::where('user_id', auth()->user()->id)->get()));
    }
    public function deleteShippingAddress($id)
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            return $this->error('User not authenticated', 401);
        }

        $address = Address::where('id', $id)->where('user_id', auth()->user()->id)->first();
        if (!$address) {
            return $this->error('Address not found', 404);
        }
        $address->delete();
        return $this->success(null, 'Shipping information has been deleted');
    }
    public function set_default($id)
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            return $this->error('User not authenticated', 401);
        }

        $addresses = Address::where('user_id', auth()->user()->id)->get();
        foreach ($addresses as $key => $address) {
            $address->set_default = 0;
            $address->save();
        }
        $address = Address::findOrFail($id);
        $address->set_default = 1;
        $address->save();

        return $this->success(null, 'Default shipping information has been updated');
    }

    public function storeCity(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'state_id' => 'required|exists:states,id',
        ]);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }

        $city = new City();
        $city->name = $request->name;
        $city->state_id = $request->state_id;
        $city->save();

        return $this->success(new CitiesCollection($city));
    }

    public function getAddresses()
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            return $this->error('User not authenticated', 401);
        }

        $addresses = Address::where('user_id', auth()->user()->id)->get();
        if (!$addresses) {
            return $this->error('No addresses found', 404);
        }
        return $this->success(new ShippingAddressesResource($addresses));
    }

    public function getAddress($id)
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            return $this->error('User not authenticated', 401);
        }

        $address = Address::where('id', $id)->where('user_id', auth()->user()->id)->first();
        if (!$address) {
            return $this->error('Address not found', 404);
        }
        return $this->success(new ShippingAddressResource($address));
    }

    public function setDefaultAddress($id)
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            return $this->error('User not authenticated', 401);
        }

        // Verify the address belongs to the authenticated user
        $address = Address::where('id', $id)->where('user_id', auth()->user()->id)->first();
        if (!$address) {
            return $this->error('Address not found', 404);
        }

        // Reset all other addresses to not default
        $addresses = Address::where('user_id', auth()->user()->id)->get();
        foreach ($addresses as $key => $addr) {
            $addr->set_default = 0;
            $addr->save();
        }

        // Set the target address as default
        $address->set_default = 1;
        $address->save();

        return $this->success(null, 'Default shipping information has been updated');
    }

    public function validateAddress(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'address' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }

        $address = Address::find($request->address);
        return $this->success(new ShippingAddressResource($address));
    }

    public function setCheckoutAddress(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'address' => 'required',
        ]);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }

        $address = Address::find($request->address);
        return $this->success(new ShippingAddressResource($address));
    }

    public function getDefaultAddress()
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            return $this->error('User not authenticated', 401);
        }

        $address = Address::where('user_id', auth()->user()->id)->where('set_default', 1)->first();
        return $this->success(new ShippingAddressResource($address));
    }
}
