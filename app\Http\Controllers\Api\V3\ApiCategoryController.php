<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Http\Resources\V2\Seller\CategoriesCollection;
use App\Http\Resources\V3\Categories\CategoriesResource;
use App\Http\Resources\V3\Categories\CategoriesTreeResource;
use App\Http\Resources\V3\Categories\CategoryResource;
use App\Http\Resources\V3\ProductsResource;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;

class ApiCategoryController extends ApiResponse
{
    public function __construct()
    {
        parent::__construct();

    }
    /**
     * List all top-level categories with their immediate subcategories
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {

        // Validate request
        $validator = \Illuminate\Support\Facades\Validator::make($request->all(), [
            'per_page' => 'nullable|integer|min:1|max:100',
            'page' => 'nullable|integer|min:1',
            'include_inactive' => 'nullable|boolean',
            'include_subcategories' => 'nullable|boolean',
            'include_products' => 'nullable|boolean',
            'product_limit' => 'nullable|integer|min:1|max:8',
            'featured' => 'nullable|boolean'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid parameters',
                $validator->errors()->messages(),
                400
            );
        }

        // Get parameters
        $per_page = min((int) $request->input('per_page', 8), 100);
        $page = max((int) $request->input('page', 1), 1);
        $include_inactive = filter_var($request->input('include_inactive', false), FILTER_VALIDATE_BOOLEAN);
        $featured = filter_var($request->input('featured', false), FILTER_VALIDATE_BOOLEAN);
        $include_subcategories = filter_var($request->input('include_subcategories', true), FILTER_VALIDATE_BOOLEAN);
        $include_products = filter_var($request->input('include_products', false), FILTER_VALIDATE_BOOLEAN);
        $product_limit = min((int) $request->input('product_limit', 4), 8);

        // Build query
        $query = Category::query();

        // Include subcategories if requested
        if ($include_subcategories) {
            $query->with('childrenCategories');
        }

        // Include products if requested
        if ($include_products) {
            $query->with([
                'products' => function ($q) use ($product_limit) {
                    $q->where('published', 1)
                        ->where('approved', 1)
                        ->take($product_limit);
                }
            ]);
        }

        // Filter by visibility
        if (!$include_inactive) {
            $query->where('is_visible', 1);
        }

        // Filter by featured status
        if ($featured) {
            $query->where('featured', 1);
        }

        // Get only top-level categories
        $query->where('parent_id', 0);

        // Order by name
        $query->orderBy('name', 'asc');

        // Paginate results
        $paginatedCategories = $query->paginate($per_page, ['*'], 'page', $page);
        $total_items = $paginatedCategories->total();
        $total_pages = $paginatedCategories->lastPage();

        // Return response
        $data = [
            'categories' => new CategoriesResource($paginatedCategories),
            'pagination' => [
                'currentPage' => (int) $page,
                'totalPages' => $total_pages,
                'totalItems' => $total_items,
                'itemsPerPage' => (int) $per_page,
            ]
        ];
        return $this->success($data);
    }
    public function details(Request $request)
    {
        $messages = array(
            'slug.required' => translate('Please enter a valid Category slug'),
            'slug.exists' => translate('Invalid Category slug.Category not found'),
        );
        $validator = Validator::make($request->all(), [
            'slug' => 'required|exists:categories,slug',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error('CATEGORY_NOT_FOUND', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }
        $include_subcategories = filter_var($request->input('include_subcategories', true), FILTER_VALIDATE_BOOLEAN);
        $include_products = filter_var($request->input('include_products', false), FILTER_VALIDATE_BOOLEAN);
        $product_limit = min((int) $request->input('product_limit', 4), 8);
        $query = Category::where('slug', $request->input('slug'));
        if ($include_subcategories) {
            $query->with('childrenCategories');
        }

        // Include products if requested
        if ($include_products) {
            $query->with([
                'products' => function ($q) use ($product_limit) {
                    $q->where('published', 1)
                        ->where('approved', 1)
                        ->take($product_limit);
                }
            ]);
        }
        $category = $query->first();
        return $this->success(
            new CategoryResource($category),
            translate('Category details fetched successfully')
        );
    }
    public function categories_tree(Request $request)
    {
        $include_inactive = $request->input('include_inactive', false);
        $categories = Category::with('childrenCategories');
        $categories->where('parent_id', 0);
        if ($include_inactive == false) {
            $categories = $categories->where('is_visible', 1);
        }
        return $this->success(
            new CategoriesTreeResource($categories->get()),
            translate('Category details fetched successfully')
        );
    }

    /**
     * Get the breadcrumb trail for a specific category
     *
     * @param Request $request
     * @param string $categoryIdentifier
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCategoryBreadcrumbs(Request $request, $categoryIdentifier)
    {
        // Find the category
        $category = null;
        if (is_numeric($categoryIdentifier)) {
            $category = Category::find($categoryIdentifier);
        } else {
            $category = Category::where('slug', $categoryIdentifier)->first();
        }

        if (!$category) {
            return $this->error(
                'CATEGORY_NOT_FOUND',
                'Category not found',
                null,
                404
            );
        }

        // Get breadcrumbs
        $breadcrumbs = [];
        $current = $category;

        // Add the current category
        $breadcrumbs[] = [
            'id' => $current->id,
            'name' => $current->name,
            'slug' => $current->slug,
            'level' => $current->level
        ];

        // Add parent categories
        while ($current->parent_id != 0) {
            $current = Category::find($current->parent_id);
            if ($current) {
                array_unshift($breadcrumbs, [
                    'id' => $current->id,
                    'name' => $current->name,
                    'slug' => $current->slug,
                    'level' => $current->level
                ]);
            } else {
                break;
            }
        }

        return $this->success($breadcrumbs);
    }

    /**
     * Get available filter options for a specific category
     *
     * @param Request $request
     * @param string $categoryIdentifier
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCategoryFilters(Request $request, $categoryIdentifier)
    {
        // Validate request
        $validator = Validator::make($request->all(), [
            'include_subcategories' => 'nullable|boolean'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid parameters',
                $validator->errors()->messages(),
                400
            );
        }

        // Get parameters
        $includeSubcategories = filter_var($request->input('include_subcategories', true), FILTER_VALIDATE_BOOLEAN);

        // Find the category
        $category = null;
        if (is_numeric($categoryIdentifier)) {
            $category = Category::find($categoryIdentifier);
        } else {
            $category = Category::where('slug', $categoryIdentifier)->first();
        }

        if (!$category) {
            return $this->error(
                'CATEGORY_NOT_FOUND',
                'Category not found',
                null,
                404
            );
        }

        // Get category IDs to include in filters
        $categoryIds = [$category->id];

        // Include subcategories if requested
        if ($includeSubcategories) {
            // Get all subcategories recursively
            $subcategoryIds = $this->getAllSubcategoryIds($category->id);
            $categoryIds = array_merge($categoryIds, $subcategoryIds);
        }

        // Get products in these categories
        $products = \App\Models\Product::whereIn('category_id', $categoryIds)
            ->where('published', 1)
            ->where('approved', 1);

        // Get brands
        $brandIds = clone $products;
        $brandIds = $brandIds->whereNotNull('brand_id')
            ->distinct()
            ->pluck('brand_id')
            ->toArray();

        $brands = \App\Models\Brand::whereIn('id', $brandIds)
            ->select('id', 'name', 'slug')
            ->withCount([
                'products' => function ($query) use ($categoryIds) {
                    $query->whereIn('category_id', $categoryIds)
                        ->where('published', 1)
                        ->where('approved', 1);
                }
            ])
            ->orderBy('name')
            ->get()
            ->map(function ($brand) {
                return [
                    'id' => $brand->id,
                    'name' => $brand->name,
                    'slug' => $brand->slug,
                    'product_count' => $brand->products_count
                ];
            });

        // Get price range
        $priceRange = [
            'min' => $products->min('unit_price') ?? 0,
            'max' => $products->max('unit_price') ?? 0
        ];

        // Get attributes from product_stock JSON attributes column
        $attributes = [];

        // Get all product stocks for products in the specified categories
        $productStocks = \App\Models\ProductStock::whereHas('product', function ($query) use ($categoryIds) {
            $query->whereIn('category_id', $categoryIds)
                ->where('published', 1)
                ->where('approved', 1);
        })->whereNotNull('attributes')->get();

        // Extract and process attributes from JSON
        $attributeValues = [];

        foreach ($productStocks as $stock) {
            // Skip if attributes is not a valid JSON
            if (empty($stock->attributes)) {
                continue;
            }

            // Decode JSON attributes
            $stockAttributes = json_decode($stock->attributes, true);

            // Skip if not an array
            if (!is_array($stockAttributes)) {
                continue;
            }

            // Process each attribute
            foreach ($stockAttributes as $attributeName => $attributeValue) {
                // Skip empty values
                if (empty($attributeValue)) {
                    continue;
                }

                // Initialize attribute if not exists
                if (!isset($attributeValues[$attributeName])) {
                    $attributeValues[$attributeName] = [];
                }

                // Initialize value if not exists
                if (!isset($attributeValues[$attributeName][$attributeValue])) {
                    $attributeValues[$attributeName][$attributeValue] = 0;
                }

                // Increment product count
                $attributeValues[$attributeName][$attributeValue]++;
            }
        }

        // Format attributes for response
        $attributeId = 1;
        foreach ($attributeValues as $attributeName => $values) {
            // Skip if no values
            if (empty($values)) {
                continue;
            }

            // Format values
            $formattedValues = [];
            foreach ($values as $value => $count) {
                $formattedValues[] = [
                    'value' => $value,
                    'product_count' => $count
                ];
            }

            // Sort values alphabetically
            usort($formattedValues, function ($a, $b) {
                return strcmp($a['value'], $b['value']);
            });

            // Add attribute to response
            $attributes[] = [
                'id' => $attributeId++,
                'name' => $attributeName,
                'values' => $formattedValues
            ];
        }

        // We've already processed attributes from product_stock JSON column



        // Get ratings
        $ratings = [];
        for ($i = 5; $i >= 1; $i--) {
            $count = \App\Models\Product::whereIn('category_id', $categoryIds)
                ->where('published', 1)
                ->where('approved', 1)
                ->where('rating', '>=', $i)
                ->where('rating', '<', $i + 1)
                ->count();

            $ratings[] = [
                'value' => $i,
                'product_count' => $count
            ];
        }

        // Return response
        return $this->success([
            'brands' => $brands,
            'price_range' => $priceRange,
            'attributes' => $attributes,
            'ratings' => $ratings
        ]);
    }

    /**
     * Get all subcategory IDs for a given category
     *
     * @param int $categoryId
     * @return array
     */
    private function getAllSubcategoryIds($categoryId)
    {
        $subcategoryIds = [];

        // Get direct subcategories
        $subcategories = Category::where('parent_id', $categoryId)->get();

        foreach ($subcategories as $subcategory) {
            $subcategoryIds[] = $subcategory->id;

            // Get subcategories of subcategories (recursive)
            $childIds = $this->getAllSubcategoryIds($subcategory->id);
            $subcategoryIds = array_merge($subcategoryIds, $childIds);
        }

        return $subcategoryIds;
    }

    /**
     * Get detailed information about a specific category
     *
     * @param Request $request
     * @param string $idOrSlug
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCategoryDetails(Request $request, $idOrSlug)
    {
        // Find the category
        $category = null;
        if (is_numeric($idOrSlug)) {
            $category = Category::find($idOrSlug);
        } else {
            $category = Category::where('slug', $idOrSlug)->first();
        }

        if (!$category) {
            return $this->error(
                'CATEGORY_NOT_FOUND',
                'Category not found',
                null,
                404
            );
        }

        // Determine what to include
        $includeSubcategories = filter_var($request->input('include_subcategories', true), FILTER_VALIDATE_BOOLEAN);
        $includeProducts = filter_var($request->input('include_products', false), FILTER_VALIDATE_BOOLEAN);
        $productLimit = min((int) $request->input('product_limit', 8), 20);

        // Load relations if needed
        if ($includeSubcategories) {
            $category->load('childrenCategories');
        }

        if ($includeProducts) {
            $category->load([
                'products' => function ($query) use ($productLimit) {
                    $query->where('published', 1)
                        ->where('approved', 1)
                        ->orderBy('created_at', 'desc')
                        ->take($productLimit);
                }
            ]);
        }

        return $this->success(
            new CategoryResource($category),
            'Category details fetched successfully'
        );
    }

    /**
     * Get featured categories
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFeaturedCategories(Request $request)
    {
        // Validate request
        $validator = Validator::make($request->all(), [
            'limit' => 'nullable|integer|min:1|max:50',
            'include_subcategories' => 'nullable|boolean',
            'include_products' => 'nullable|boolean',
            'product_limit' => 'nullable|integer|min:1|max:8',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid parameters',
                $validator->errors()->messages(),
                400
            );
        }

        // Get parameters
        $limit = min((int) $request->input('limit', 10), 50);
        $includeSubcategories = filter_var($request->input('include_subcategories', true), FILTER_VALIDATE_BOOLEAN);
        $includeProducts = filter_var($request->input('include_products', false), FILTER_VALIDATE_BOOLEAN);
        $productLimit = min((int) $request->input('product_limit', 4), 8);

        // Build query
        $query = Category::where('featured', 1)
            ->where('is_visible', 1);

        // Include subcategories if requested
        if ($includeSubcategories) {
            $query->with('childrenCategories');
        }

        // Include products if requested
        if ($includeProducts) {
            $query->with([
                'products' => function ($q) use ($productLimit) {
                    $q->where('published', 1)
                        ->where('approved', 1)
                        ->take($productLimit);
                }
            ]);
        }

        // Get results
        $categories = $query->orderBy('order_level', 'asc')
            ->take($limit)
            ->get();

        return $this->success(
            new CategoriesResource($categories),
            'Featured categories fetched successfully'
        );
    }

    /**
     * Get subcategories for a specific category
     *
     * @param Request $request
     * @param string $idOrSlug
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCategorySubcategories(Request $request, $idOrSlug)
    {
        // Find the category
        $category = null;
        if (is_numeric($idOrSlug)) {
            $category = Category::find($idOrSlug);
        } else {
            $category = Category::where('slug', $idOrSlug)->first();
        }

        if (!$category) {
            return $this->error(
                'CATEGORY_NOT_FOUND',
                'Category not found',
                null,
                404
            );
        }

        // Validate request
        $validator = Validator::make($request->all(), [
            'include_inactive' => 'nullable|boolean',
            'include_products' => 'nullable|boolean',
            'product_limit' => 'nullable|integer|min:1|max:8',
            'per_page' => 'nullable|integer|min:1|max:100',
            'page' => 'nullable|integer|min:1',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid parameters',
                $validator->errors()->messages(),
                400
            );
        }

        // Get parameters
        $includeInactive = filter_var($request->input('include_inactive', false), FILTER_VALIDATE_BOOLEAN);
        $includeProducts = filter_var($request->input('include_products', false), FILTER_VALIDATE_BOOLEAN);
        $productLimit = min((int) $request->input('product_limit', 4), 8);
        $perPage = min((int) $request->input('per_page', 20), 100);
        $page = max((int) $request->input('page', 1), 1);

        // Build subcategories query
        $query = Category::where('parent_id', $category->id);

        // Filter by visibility
        if (!$includeInactive) {
            $query->where('is_visible', 1);
        }

        // Include products if requested
        if ($includeProducts) {
            $query->with([
                'products' => function ($q) use ($productLimit) {
                    $q->where('published', 1)
                        ->where('approved', 1)
                        ->take($productLimit);
                }
            ]);
        }

        // Get paginated results
        $subcategories = $query->orderBy('name', 'asc')
            ->paginate($perPage, ['*'], 'page', $page);

        return $this->success([
            'parent_category' => [
                'id' => $category->id,
                'name' => $category->name,
                'slug' => $category->slug,
            ],
            'subcategories' => new CategoriesResource($subcategories),
            'pagination' => [
                'current_page' => $subcategories->currentPage(),
                'last_page' => $subcategories->lastPage(),
                'per_page' => $subcategories->perPage(),
                'total' => $subcategories->total()
            ]
        ]);
    }

    /**
     * Get products for a specific category
     *
     * @param Request $request
     * @param string $idOrSlug
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCategoryProducts(Request $request, $idOrSlug)
    {
        // Find the category
        $category = null;
        if (is_numeric($idOrSlug)) {
            $category = Category::find($idOrSlug);
        } else {
            $category = Category::where('slug', $idOrSlug)->first();
        }

        if (!$category) {
            return $this->error(
                'CATEGORY_NOT_FOUND',
                'Category not found',
                null,
                404
            );
        }

        // Validate request
        $validator = Validator::make($request->all(), [
            'per_page' => 'nullable|integer|min:1|max:100',
            'page' => 'nullable|integer|min:1',
            'sort_by' => 'nullable|string|in:newest,oldest,price_asc,price_desc,name_asc,name_desc,popularity',
            'include_subcategories' => 'nullable|boolean',
            'min_price' => 'nullable|numeric|min:0',
            'max_price' => 'nullable|numeric|min:0',
            'brand_ids' => 'nullable|string',
            'rating' => 'nullable|integer|min:1|max:5',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid parameters',
                $validator->errors()->messages(),
                400
            );
        }

        // Get parameters
        $perPage = min((int) $request->input('per_page', 20), 100);
        $page = max((int) $request->input('page', 1), 1);
        $sortBy = $request->input('sort_by', 'newest');
        $includeSubcategories = filter_var($request->input('include_subcategories', true), FILTER_VALIDATE_BOOLEAN);
        $minPrice = $request->input('min_price');
        $maxPrice = $request->input('max_price');
        $brandIds = $request->input('brand_ids') ? explode(',', $request->input('brand_ids')) : null;
        $rating = $request->input('rating');

        // Get all relevant category IDs
        $categoryIds = [$category->id];
        if ($includeSubcategories) {
            $subcategoryIds = $this->getAllSubcategoryIds($category->id);
            $categoryIds = array_merge($categoryIds, $subcategoryIds);
        }

        // Build product query
        $productQuery = \App\Models\Product::whereIn('category_id', $categoryIds)
            ->where('published', 1)
            ->where('approved', 1);

        // Apply price filters
        if ($minPrice !== null) {
            $productQuery->where('unit_price', '>=', $minPrice);
        }
        if ($maxPrice !== null) {
            $productQuery->where('unit_price', '<=', $maxPrice);
        }

        // Apply brand filter
        if ($brandIds) {
            $productQuery->whereIn('brand_id', $brandIds);
        }

        // Apply rating filter
        if ($rating) {
            $productQuery->where('rating', '>=', $rating);
        }

        // Apply sorting
        switch ($sortBy) {
            case 'newest':
                $productQuery->orderBy('created_at', 'desc');
                break;
            case 'oldest':
                $productQuery->orderBy('created_at', 'asc');
                break;
            case 'price_asc':
                $productQuery->orderBy('unit_price', 'asc');
                break;
            case 'price_desc':
                $productQuery->orderBy('unit_price', 'desc');
                break;
            case 'name_asc':
                $productQuery->orderBy('name', 'asc');
                break;
            case 'name_desc':
                $productQuery->orderBy('name', 'desc');
                break;
            case 'popularity':
                $productQuery->orderBy('num_of_sale', 'desc');
                break;
            default:
                $productQuery->orderBy('created_at', 'desc');
        }

        // Get paginated products
        $products = $productQuery->paginate($perPage, ['*'], 'page', $page);

        // Format response
        return $this->success([
            'category' => [
                'id' => $category->id,
                'name' => $category->name,
                'slug' => $category->slug,
            ],
            'products' => new \App\Http\Resources\V3\ProductsResource($products),
            'pagination' => [
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total()
            ]
        ]);
    }

    /**
     * Get all subcategories for a specific category
     *
     * @param Request $request
     * @param string $idOrSlug
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSubcategories(Request $request, $idOrSlug)
    {
        // Find the category
        $category = null;
        if (is_numeric($idOrSlug)) {
            $category = Category::find($idOrSlug);
        } else {
            $category = Category::where('slug', $idOrSlug)->first();
        }

        if (!$category) {
            return $this->error(
                'CATEGORY_NOT_FOUND',
                'Category not found',
                null,
                404
            );
        }

        // Get all subcategories
        $subcategories = Category::where('parent_id', $category->id)
            ->where('is_visible', 1)
            ->orderBy('name', 'asc')
            ->get();
        // Get subcategories with their immediate subcategories
        $subcategoriesWithSubcategories = [];
        foreach ($subcategories as $subcategory) {
            $subcategory->subcategories = Category::where('parent_id', $subcategory->id)
                ->where('is_visible', 1)
                ->orderBy('name', 'asc')
                ->get();
            $subcategoriesWithSubcategories[] = $subcategory;
        }

        return $this->success(
            $subcategoriesWithSubcategories,
            'Subcategories fetched successfully'
        );
    }

    /**
     * Get category sections for navigation or display
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCategorySections(Request $request)
    {
        // Get input params with sane defaults
        $featuredOnly = filter_var($request->input('featured_only', true), FILTER_VALIDATE_BOOLEAN);
        $includeProductCount = filter_var($request->input('include_product_count', true), FILTER_VALIDATE_BOOLEAN);
        $limit = min((int) $request->input('limit', 10), 50);

        // Consistent cache key
        $cacheKey = sprintf(
            'home_page_category_menu:featured_%d:product_count_%d:limit_%d',
            $featuredOnly,
            $includeProductCount,
            $limit
        );

        // 1 hour cache
        $cacheTTL = 60 * 60* 24 * 7;

        $data = Cache::remember($cacheKey, $cacheTTL, function () use ($featuredOnly, $includeProductCount, $limit) {
            // Base query
            $query = Category::where('parent_id', 0)
                ->where('is_visible', 1);

            if ($featuredOnly) {
                $query->where('featured', 1);
            }

            // Relations: eager-load only needed branches
            $with = ['childrenCategories.childrenCategories'];

            if ($includeProductCount) {
                // Load counts efficiently — use `withCount` instead of loading full product lists
                $query->withCount('products');
                $with[] = 'childrenCategories.products:id,category_id';
                $with[] = 'childrenCategories.childrenCategories.products:id,category_id';
            }

            $query->with($with);

            // Fetch
            $categories = $query->orderBy('order_level')->take($limit)->get();

            // Format
            return $categories->map(function ($category) use ($includeProductCount) {
                return [
                    'id' => (string) $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'image' => $category->banner ?? $category->icon,
                    'description' => $category->meta_description ?? $category->name,
                    'parentId' => null,
                    'isFeatured' => (bool) $category->featured,
                    'displayOrder' => $category->order_level ?? $category->level ?? 0,
                    'productCount' => $includeProductCount ? $category->products_count ?? 0 : null,
                    'children' => $category->childrenCategories->map(function ($child) use ($includeProductCount, $category) {
                        return [
                            'id' => (string) $child->id,
                            'name' => $child->name,
                            'slug' => $child->slug,
                            'image' => $child->banner ?? $child->icon,
                            'parentId' => (string) $category->id,
                            'displayOrder' => $child->order_level ?? $child->level ?? 0,
                            'productCount' => $includeProductCount ? $child->products->count() : null,
                            'children' => $child->childrenCategories->map(function ($grandchild) use ($includeProductCount, $child) {
                                return [
                                    'id' => (string) $grandchild->id,
                                    'name' => $grandchild->name,
                                    'slug' => $grandchild->slug,
                                    'image' => $grandchild->banner ?? $grandchild->icon,
                                    'parentId' => (string) $child->id,
                                    'displayOrder' => $grandchild->order_level ?? $grandchild->level ?? 0,
                                    'productCount' => $includeProductCount ? $grandchild->products->count() : null,
                                ];
                            })->values()->all(),
                        ];
                    })->values()->all(),
                ];
            })->values()->all();
        });


        return $this->withCacheControl($cacheTTL, false)->success(
            $data,
            'Category sections fetched successfully'
        );
    }

}
