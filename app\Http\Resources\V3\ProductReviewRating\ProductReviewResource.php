<?php

namespace App\Http\Resources\V3\ProductReviewRating;

use Illuminate\Http\Resources\Json\JsonResource;

class ProductReviewResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id'=> $this->id,
            'user_id'=> $this->user ? $this->user->id : null,
            'user_name'=> $this->user ? $this->user->name : null,
            'avatar'=> $this->user && $this->user->avatar_original ? uploaded_asset($this->user->avatar_original) : null,
            'reviewImage'=> $this->getReviewImages(),
            // 'reviewVideo'=> uploaded_asset($this->photos),
            'rating' => $this->rating ? floatval(number_format($this->rating,1,'.','')) : 0,
            'comment' => $this->comment ?? '',
            'review_title' => $this->review_title ?? '',
            'tags' => $this->getReviewTags(),
            'status' => (boolean)($this->status ?? false),
            'reviewTime' => $this->updated_at ? $this->updated_at->format('M j, Y \\A\\T H:i') : null,
            'created_at' => $this->created_at ? $this->created_at->toISOString() : null,
            // Product details
            'product' => $this->getProductDetails(),
            // Additional review data
            'helpful_count' => $this->helpful_count ?? 0,
            'seller_response' => $this->seller_response ?? null,
            'seller_response_date' => $this->seller_response_date ? $this->seller_response_date->format('M j, Y \\A\\T H:i') : null,
            'verified' => (boolean)($this->verified ?? false),
        ];
    }

    /**
     * Get review images handling both array and string formats
     */
    private function getReviewImages()
    {
        if (!$this->photos) {
            return [];
        }

        // If photos is already an array (from cast)
        if (is_array($this->photos)) {
            return array_map(function ($image) {
                return uploaded_asset($image);
            }, $this->photos);
        }

        // If photos is a comma-separated string
        if (is_string($this->photos)) {
            $images = explode(',', $this->photos);
            return array_map(function ($image) {
                return uploaded_asset(trim($image));
            }, array_filter($images));
        }

        return [];
    }

    /**
     * Get review tags handling both array and string formats
     */
    private function getReviewTags()
    {
        if (!$this->tags) {
            return [];
        }

        // If tags is already an array (from cast)
        if (is_array($this->tags)) {
            return $this->tags;
        }

        // If tags is a JSON string
        if (is_string($this->tags)) {
            $decoded = json_decode($this->tags, true);
            return is_array($decoded) ? $decoded : [];
        }

        return [];
    }

    /**
     * Get product details with null safety
     */
    private function getProductDetails()
    {
        if (!$this->product) {
            return null;
        }

        return [
            'id' => $this->product->id ?? null,
            'name' => $this->product->name ?? null,
            'slug' => $this->product->slug ?? null,
            'thumbnail_img' => $this->product->thumbnail_img ? uploaded_asset($this->product->thumbnail_img) : null,
            'min_qty' => $this->product->min_qty ?? 0,
            'unit_price' => $this->product->unit_price ?? 0,
            'discount' => $this->product->discount ?? 0,
            'discount_type' => $this->product->discount_type ?? null,
            'category' => [
                'id' => $this->product->category->id ?? null,
                'name' => $this->product->category->name ?? null,
            ] 
        ];
    }
}
