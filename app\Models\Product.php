<?php

namespace App\Models;

use App;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{

    protected $guarded = ['choice_attributes'];

    protected $casts = [
        'is_premium' => 'boolean',
        'is_early_access' => 'boolean',
        'early_access_expiry' => 'datetime',
        'is_top_tier' => 'boolean',
        'is_limited_offer' => 'boolean',
        'offer_start_date' => 'datetime',
        'offer_end_date' => 'datetime',
        'margin' => 'float',
        'original_price' => 'float',
        'is_bundle' => 'boolean',
        'bundle_items' => 'array',
        'savings_amount' => 'float',
        'savings_percentage' => 'float',
    ];

    protected $with = ['product_translations', 'taxes', 'thumbnail', 'supplier'];

    public function getTranslation($field = '', $lang = false)
    {
        $lang = $lang == false ? App::getLocale() : $lang;
        $product_translations = $this->product_translations->where('lang', $lang)->first();
        return $product_translations != null ? $product_translations->$field : $this->$field;
    }

    public function product_translations()
    {
        return $this->hasMany(ProductTranslation::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function orderDetails()
    {
        return $this->hasMany(OrderDetail::class);
    }

    public function reviews()
    {
        return $this->hasMany(Review::class)->where('status', 1);
    }
    public function getApprovedReviewCount(): int
    {
        return $this->reviews()->whereNotNull('comment')->count();
    }
    public function getApprovedRatingCount(): int
    {
        return $this->reviews()->whereNotNull('rating')->count();
    }

    public function wishlists()
    {
        return $this->hasMany(Wishlist::class);
    }

    public function stocks()
    {
        return $this->hasMany(ProductStock::class);
    }
    public function getTotalStockQuantity(): int
    {
        return $this->stocks()->sum('qty');
    }

    public function taxes()
    {
        return $this->hasMany(ProductTax::class);
    }

    public function flash_deal_product()
    {
        return $this->hasOne(FlashDealProduct::class);
    }

    public function bids()
    {
        return $this->hasMany(AuctionProductBid::class);
    }

    public function thumbnail()
    {
        return $this->belongsTo(Upload::class, 'thumbnail_img');
    }

    public function scopePhysical($query)
    {
        return $query->where('digital', 0);
    }

    public function scopeDigital($query)
    {
        return $query->where('digital', 1);
    }

    public function carts()
    {
        return $this->hasMany(Cart::class);
    }

    public function scopeIsApprovedPublished($query)
    {
        return $query->where('approved', '1')->where('published', 1);
    }

    public function productStock()
    {
        return $this->hasOne(ProductStock::class);
    }

    public function productStocks()
    {
        return $this->hasMany(ProductStock::class);
    }
    // added by arif on 4 june 2024


    public function getChoiceOptionsAttribute($value)
    {
        return json_decode($value, true);
    }
    
    /**
     * Get photos as Upload collection based on comma-separated IDs
     */
    public function photosRelation()
    {
        if (empty($this->photos)) {
            return collect([]);
        }
        
        $photoIds = explode(',', $this->photos);
        $photoIds = array_filter(array_map('trim', $photoIds));
        
        if (empty($photoIds)) {
            return collect([]);
        }
        
        return Upload::whereIn('id', $photoIds)->get();
    }
    // added by arif on 4 june 2024 end

    // added by arif on 25 june 2024 start
    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }
    // added by arif on 25 june 2024 end
    public function hasReturnPolicy()
    {
        return !$this->category || !$this->category->isGroceryCategory();
    }
    public function product_videos()
    {
        return $this->morphMany(Video::class, 'subject');
    }

    /**
     * Scope a query to only include bundle products.
     */
    public function scopeBundle($query)
    {
        return $query->where('is_bundle', true);
    }

    /**
     * Get the bundle items with product details.
     */
    public function getBundleItemsWithDetailsAttribute()
    {
        if (!$this->is_bundle || empty($this->bundle_items)) {
            return collect([]);
        }
        
        $bundleItems = collect($this->bundle_items);
        
        return $bundleItems->map(function ($item) {
            $product = Product::find($item['product_id']);
            return [
                'product_id' => $item['product_id'],
                'product' => $product,
                'quantity' => $item['quantity'] ?? 1,
                'price' => $item['price'] ?? 0,
            ];
        });
    }
}
