<?php

namespace App\Observers;

use App\Models\ReturnRequestInfo;
use App\Services\NotificationService;
use App\Enums\NotificationType;
use App\Enums\NotificationPriority;

class ReturnRequestObserver
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Handle the ReturnRequestInfo "created" event.
     */
    public function created(ReturnRequestInfo $returnRequest)
    {
        // Send notification to admin when new return request is created
        $this->notificationService->sendAdminNotification(
            title: "New Return Request - #{$returnRequest->id}",
            message: "A new return request has been submitted by {$returnRequest->user->name} for order #{$returnRequest->order_id}",
            type: NotificationType::ORDER,
            priority: NotificationPriority::HIGH,
            link: route('all_orders.show', $returnRequest->order_id),
            linkText: 'View Return Request',
            subject: $returnRequest,
            userId: $returnRequest->user_id
        );

        //Send confirmation notification to customer
        // $this->notificationService->sendNotification(
        //     user: $returnRequest->user,
        //     title: "Return Request Submitted - #{$returnRequest->id}",
        //     message: "Your return request has been submitted successfully. We will review it shortly.",
        //     type: NotificationType::ORDER,
        //     priority: NotificationPriority::MEDIUM,
        //     link: "/return-requests/{$returnRequest->id}",
        //     linkText: 'View Request',
        //     subject: $returnRequest
        // );
    }

    /**
     * Handle the ReturnRequestInfo "updated" event.
     */
    public function updated(ReturnRequestInfo $returnRequest)
    {
        $changes = $returnRequest->getDirty();
        
        // Check if status changed
        if (isset($changes['status'])) {
            $previousStatus = $returnRequest->getOriginal('status');
            $newStatus = $returnRequest->status;
            
            // Send notification to customer about status change
            $this->notificationService->sendNotification(
                user: $returnRequest->user,
                title: "Return Request Status Updated - #{$returnRequest->id}",
                message: "Your return request status has been changed from {$previousStatus} to {$newStatus}",
                type: NotificationType::ORDER,
                priority: $this->getPriorityFromStatus($newStatus),
                link: "/return-requests/{$returnRequest->id}",
                linkText: 'View Request',
                subject: $returnRequest
            );

            // Send notification to admin about status change
            $this->notificationService->sendAdminNotification(
                title: "Return Request Status Updated - #{$returnRequest->id}",
                message: "Return request #{$returnRequest->id} status changed from {$previousStatus} to {$newStatus}",
                type: NotificationType::ORDER,
                priority: NotificationPriority::MEDIUM,
                link: route('all_orders.show', $returnRequest->order_id),
                linkText: 'View Request',
                subject: $returnRequest,
                userId: $returnRequest->user_id
            );
        }

        // Check if refund amount changed
        if (isset($changes['refund_amount'])) {
            $refundAmount = $returnRequest->refund_amount;
            
            // Send notification to admin about refund amount update
            $this->notificationService->sendAdminNotification(
                title: "Return Request Refund Updated - #{$returnRequest->id}",
                message: "Refund amount updated to " . single_price($refundAmount) . " for return request #{$returnRequest->id}",
                type: NotificationType::PAYMENT,
                priority: NotificationPriority::HIGH,
                link: route('all_orders.show', $returnRequest->order_id),
                linkText: 'View Request',
                subject: $returnRequest,
                userId: $returnRequest->user_id
            );
        }
    }

    /**
     * Get notification priority based on return request status
     */
    private function getPriorityFromStatus(string $status): string
    {
        return match(strtolower($status)) {
            'approved', 'refunded' => NotificationPriority::HIGH,
            'rejected', 'cancelled' => NotificationPriority::MEDIUM,
            'pending', 'processing' => NotificationPriority::MEDIUM,
            default => NotificationPriority::LOW,
        };
    }
} 