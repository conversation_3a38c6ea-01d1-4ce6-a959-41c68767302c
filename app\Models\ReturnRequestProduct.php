<?php

namespace App\Models;

use App\Enums\ReturnStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReturnRequestProduct extends Model
{
    use HasFactory;

    protected $table = 'return_request_products';

    protected $fillable = [
        'return_request_info_id',
        'order_detail_id',
        'product_id',
        'quantity',
        'unit_price',
        'amount',
        'return_status',
        'seller_id',
        'seller_approval',
        'admin_note_for_product',
        'seller_note_for_product',
    ];
    protected $casts = [
        'return_status' => ReturnStatus::class,
        'seller_approval' => 'boolean',
    ];
    
    public function returnRequestInfo()
    {
        return $this->belongsTo(ReturnRequestInfo::class, 'return_request_info_id');
    }
    
    public function orderDetail()
    {
        return $this->belongsTo(OrderDetail::class, 'order_detail_id');
    }
    
    public function product()
    {
        return $this->belongsTo(Product::class);
    }
    
    public function seller()
    {
        return $this->belongsTo(User::class, 'seller_id');
    }

}
