<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class UserOfferUsage extends Model
{
    protected $table = 'user_offer_usage';

    protected $fillable = [
        'user_id',
        'offer_id',
        'coupon_code',
        'usage_count',
        'usage_limit',
        'minimum_purchase_amount',
        'discount_amount_used',
        'total_savings',
        'first_used_at',
        'last_used_at',
        'expires_at',
        'is_active',
        'usage_history'
    ];

    protected $casts = [
        'usage_count' => 'integer',
        'usage_limit' => 'integer',
        'minimum_purchase_amount' => 'decimal:2',
        'discount_amount_used' => 'decimal:2',
        'total_savings' => 'decimal:2',
        'first_used_at' => 'datetime',
        'last_used_at' => 'datetime',
        'expires_at' => 'datetime',
        'is_active' => 'boolean',
        'usage_history' => 'json'
    ];

    /**
     * Get the user that owns this offer usage
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the offer that this usage is for
     */
    public function offer()
    {
        return $this->belongsTo(Offer::class);
    }

    /**
     * Check if this user offer usage has reached its limit
     */
    public function hasReachedLimit(): bool
    {
        return $this->usage_count >= $this->usage_limit;
    }

    /**
     * Check if this user offer usage is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at && Carbon::now()->isAfter($this->expires_at);
    }

    /**
     * Check if this user offer usage is valid (active, not expired, not reached limit)
     */
    public function isValid(): bool
    {
        return $this->is_active && !$this->isExpired() && !$this->hasReachedLimit();
    }

    /**
     * Record a usage of this offer
     */
    public function recordUsage(float $discountAmount, string $orderId = null): void
    {
        $now = Carbon::now();
        
        // Update usage count
        $this->usage_count++;
        
        // Set first used date if this is the first usage
        if ($this->first_used_at === null) {
            $this->first_used_at = $now;
        }
        
        // Update last used date
        $this->last_used_at = $now;
        
        // Update totals
        $this->discount_amount_used += $discountAmount;
        $this->total_savings += $discountAmount;
        
        // Add to usage history
        $history = $this->usage_history ?? [];
        $history[] = [
            'used_at' => $now->toDateTimeString(),
            'discount_amount' => $discountAmount,
            'order_id' => $orderId
        ];
        $this->usage_history = $history;
        
        // Deactivate if usage limit reached
        if ($this->hasReachedLimit()) {
            $this->is_active = false;
        }
        
        $this->save();
    }

    /**
     * Generate a unique coupon code for this user offer usage
     */
    public static function generateCouponCode(string $offerPromoCode, int $userId): string
    {
        // Create a unique coupon code based on offer promo code and user ID
        $base = strtoupper($offerPromoCode) . '_' . $userId;
        $hash = substr(md5($base . time()), 0, 6);
        return $base . '_' . $hash;
    }

    /**
     * Create or update user offer usage when applying an offer
     */
    public static function createOrUpdateFromOffer(Offer $offer, User $user, string $couponCode = null): self
    {
        // Check if user already has this offer
        $userOfferUsage = static::where('user_id', $user->id)
            ->where('offer_id', $offer->id)
            ->first();

        if (!$userOfferUsage) {
            // Use provided coupon code or generate one
            if (!$couponCode) {
                $couponCode = static::generateCouponCode($offer->promo_code, $user->id);
            }
            
            $userOfferUsage = static::create([
                'user_id' => $user->id,
                'offer_id' => $offer->id,
                'coupon_code' => $couponCode,
                'usage_count' => 0,
                'usage_limit' => $offer->usage_limit ?: 1,
                'minimum_purchase_amount' => $offer->min_order_value ?: 0,
                'expires_at' => $offer->end_date,
                'is_active' => true
            ]);
        }

        return $userOfferUsage;
    }
} 