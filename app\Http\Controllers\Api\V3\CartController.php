<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Services\EnhancedCartService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class CartController extends Controller
{
    protected $cartService;
    protected $enhancedCartService;

    public function __construct(EnhancedCartService $cartService, EnhancedCartService $enhancedCartService)
    {
        $this->cartService = $cartService;
        $this->enhancedCartService = $enhancedCartService;
    }

    /**
     * Get current cart
     */
    public function index(Request $request): JsonResponse
    {
        try {
            
            // Try to get user ID from different sources
            $userId = Auth::guard('sanctum')->id() ?? Auth::id() ?? $request->user()?->id ?? $request->attributes->get('authenticated_user')?->id;
            
            if (!$userId) {
                $tempUserId = $this->getTempUserId($request);
            } else {
                $tempUserId = null;
            }

            Log::info('Cart index - User identification', [
                'user_id' => $userId,
                'temp_user_id' => $tempUserId
            ]);

            $cart = $this->cartService->getCart($userId, $tempUserId, $request);
            
            // Add user identification to the cart data
            $cart['user_id'] = $userId;
            $cart['temp_user_id'] = $tempUserId;
           
            return response()->json([
                'status' => 'success',
                'data' => $cart,
                'temp_user_id' => $tempUserId,
                'user_id' => $userId
            ]);

        } catch (\Exception $e) {
            Log::error('Cart index error: ' . $e->getMessage());
        
        return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve cart',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Add item to cart
     */
    public function addToCart(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'product_id' => 'required|integer|exists:products,id',
                'quantity' => 'integer|min:1|max:999',
                'variation' => 'nullable|string',
                'options' => 'nullable|array'
            ]);
        
            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
        
            $userId = Auth::id();
            $tempUserId = $this->getTempUserId($request);

            $result = $this->cartService->addToCart(
                $request->product_id,
                $request->quantity ?? 1,
                $request->variation,
                $request->options ?? [],
                $userId,
                $tempUserId,
                $request
            );

            if ($result['success']) {
                return response()->json([
                    'status' => 'success',
                    'message' => $result['message'],
                    'data' => [
                        'cart' => $result['cart'],
                        'added_item' => $result['added_item']
                    ],
                    'temp_user_id' => $tempUserId
                ]);
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => $result['message']
                ], 400);
            }
        
        } catch (\Exception $e) {
            // Try to log but don't let logging failures break the API response
            try {
                Log::error('Add to cart error: ' . $e->getMessage(), [
                    'exception' => $e,
                    'trace' => $e->getTraceAsString(),
                    'request' => $request->all()
                ]);
            } catch (\Exception $logException) {
                // Logging failed, but don't let it break the API response
                // Continue to return proper error response
            }
            
            // Always return a proper JSON response, never an empty response
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to add item to cart',
                'error' => $e->getMessage(),
                'error_code' => $e->getCode()
            ], 500);
        }
    }
    
    /**
     * Update cart item quantity
     */
    public function updateItem(Request $request, $itemId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'quantity' => 'required|integer|min:0|max:999'
            ]);
        
            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
        
            $userId = Auth::id();
            $tempUserId = $this->getTempUserId($request);
        
            $result = $this->enhancedCartService->updateCartItem(
                $itemId,
                $request->quantity,
                $userId,
                $tempUserId,
                $request
            );

            if ($result['success']) {
                return response()->json([
                    'status' => 'success',
                    'message' => $result['message'],
                    'data' => [
                        'cart' => $result['cart'],
                        'updated_item' => $result['updated_item'] ?? null
                    ]
                ]);
            } else {
                // For debugging purposes, return the actual error message
                return response()->json([
                    'status' => 'error',
                    'message' => $result['message'],
                    'error_code' => 'UPDATE_FAILED',
                    'result' => $result
                ], 400);
            }
        
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            Log::info('Cart item not found for update: ' . $itemId);
            
            // Return a friendly message instead of an error
            return response()->json([
                'status' => 'error',
                'message' => 'Item not found or already removed',
                'data' => [
                    'cart' => $this->cartService->getCart(Auth::id(), $this->getTempUserId($request), $request)
                ]
            ], 404);
        } catch (\Exception $e) {
            Log::error('Update cart item error: ' . $e->getMessage(), [
                'item_id' => $itemId,
                'exception' => get_class($e),
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update item quantity. Please try again.',
                'error_details' => app()->environment('production') ? null : [
                    'exception' => get_class($e),
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]
            ], 500);
        }
    }
    
    /**
     * Remove item from cart
     */
    public function removeItem(Request $request, $itemId): JsonResponse
    {
        try {
            $userId = Auth::id();
            $tempUserId = $this->getTempUserId($request);
        
            $result = $this->cartService->removeCartItem(
                $itemId,
                $userId,
                $tempUserId,
                $request
            );
            
            if ($result['success']) {
                return response()->json([
                    'status' => 'success',
                    'message' => $result['message'],
                    'data' => [
                        'cart' => $result['cart']
                    ]
                ]);
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => $result['message']
                ], 400);
            }
        
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            Log::info('Cart item not found: ' . $itemId);
            
            // Return a friendly message instead of an error
            return response()->json([
                'status' => 'success',
                'message' => 'Item already removed or does not exist',
                'data' => [
                    'cart' => $this->cartService->getCart(Auth::id(), $this->getTempUserId($request), $request)
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Remove cart item error: ' . $e->getMessage());
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to remove cart item',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Clear entire cart
     */
    public function clear(Request $request): JsonResponse
    {
        try {
            $userId = Auth::id();
            $tempUserId = $this->getTempUserId($request);

            $result = $this->cartService->clearCart($userId, $tempUserId, $request);

            if ($result['success']) {
                return response()->json([
                    'status' => 'success',
                    'message' => $result['message'],
                    'data' => [
                        'cart' => $result['cart']
                    ]
                ]);
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => $result['message']
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('Clear cart error: ' . $e->getMessage());
        
        return response()->json([
                'status' => 'error',
                'message' => 'Failed to clear cart',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Save item for later
     */
    public function saveForLater(Request $request, $itemId): JsonResponse
    {
        try {
            $userId = Auth::id();
            $tempUserId = $this->getTempUserId($request);

            $result = $this->cartService->saveForLater(
                $itemId,
                $userId,
                $tempUserId,
                $request
            );
        
            if ($result['success']) {
                return response()->json([
                    'status' => 'success',
                    'message' => $result['message'],
                    'data' => [
                        'cart' => $result['cart'],
                        'saved_item' => $result['saved_item'] ?? null
                    ]
                ]);
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => $result['message']
                ], 400);
            }

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            Log::info('Cart item not found for save-for-later: ' . $itemId);
            
            // Return a friendly message instead of an error
            return response()->json([
                'status' => 'success',
                'message' => 'Item not found or already removed',
                'data' => [
                    'cart' => $this->cartService->getCart(Auth::id(), $this->getTempUserId($request), $request)
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Save for later error: ' . $e->getMessage());
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to save item for later',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Move saved item back to cart
     */
    public function moveToCart(Request $request, $savedItemId): JsonResponse
    {
        try {
            $userId = Auth::id();
            $tempUserId = $this->getTempUserId($request);

            $result = $this->cartService->moveToCart(
                $savedItemId,
                $userId,
                $tempUserId,
                $request
            );

            if ($result['success']) {
                return response()->json([
                    'status' => 'success',
                    'message' => $result['message'],
                    'data' => [
                        'cart' => $result['cart']
                    ]
                ]);
                } else {
            return response()->json([
                    'status' => 'error',
                    'message' => $result['message']
            ], 400);
        }
        
        } catch (\Exception $e) {
            Log::error('Move to cart error: ' . $e->getMessage());
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to move item to cart',
                'error' => $e->getMessage()
            ], 500);
        }
        }
        
    /**
     * Get saved items
     */
    public function savedItems(Request $request): JsonResponse
    {
        try {
            $userId = Auth::id();
            $tempUserId = $this->getTempUserId($request);

            $savedItems = $this->cartService->getSavedItems($userId, $tempUserId);

            return response()->json([
                'status' => 'success',
                'data' => [
                    'saved_items' => $savedItems,
                    'count' => count($savedItems)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Get saved items error: ' . $e->getMessage());
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve saved items',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Remove saved item
     */
    public function removeSavedItem(Request $request, $savedItemId): JsonResponse
    {
        try {
            $userId = Auth::id();
            $tempUserId = $this->getTempUserId($request);

            $result = $this->cartService->removeSavedItem($savedItemId, $userId, $tempUserId);

            if ($result['success']) {
        return response()->json([
                    'status' => 'success',
                    'message' => $result['message']
                ]);
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => $result['message']
                ], 400);
    }
    
        } catch (\Exception $e) {
            Log::error('Remove saved item error: ' . $e->getMessage());
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to remove saved item',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Apply coupon to cart
     */
    public function applyCoupon(Request $request): JsonResponse
    {
        try {
        $validator = Validator::make($request->all(), [
                'coupon_code' => 'required|string|max:50'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
            $userId = Auth::id();
            $tempUserId = $this->getTempUserId($request);

            $result = $this->cartService->applyCoupon(
                $request->coupon_code,
                $userId,
                $tempUserId,
                $request
            );

            if ($result['success']) {
            return response()->json([
                    'status' => 'success',
                    'message' => $result['message'],
                    'data' => [
                        'cart' => $result['cart'],
                        'coupon' => $result['coupon']
                    ]
                ]);
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => $result['message']
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('Apply coupon error: ' . $e->getMessage());
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to apply coupon',
                'error' => $e->getMessage()
            ], 500);
        }
        }
        
    /**
     * Remove coupon from cart
     */
    public function removeCoupon(Request $request): JsonResponse
    {
        try {
            $userId = Auth::id();
            $tempUserId = $this->getTempUserId($request);

            $result = $this->cartService->removeCoupon($userId, $tempUserId, $request);

            if ($result['success']) {
                return response()->json([
                    'status' => 'success',
                    'message' => $result['message'],
                    'data' => [
                        'cart' => $result['cart']
                    ]
                ]);
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => $result['message']
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('Remove coupon error: ' . $e->getMessage());
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to remove coupon',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Merge guest cart with user cart after login
     */
    public function mergeCart(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'temp_user_id' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $userId = Auth::id();
            if (!$userId) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'User must be authenticated to merge cart'
                ], 401);
    }
    
            $result = $this->cartService->mergeCartsAfterLogin(
                $userId,
                $request->temp_user_id,
                $request
            );

            if ($result['success']) {
                return response()->json([
                    'status' => 'success',
                    'message' => $result['message'],
                    'data' => [
                        'cart' => $result['cart'],
                        'merged_items_count' => $result['merged_items_count'] ?? 0,
                        'conflict_items_count' => $result['conflict_items_count'] ?? 0
                    ]
                ]);
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => $result['message']
                ], 400);
        }
        
        } catch (\Exception $e) {
            Log::error('Merge cart error: ' . $e->getMessage());
        
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to merge cart',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get cart count (for header/badge display)
     */
    public function count(Request $request): JsonResponse
    {
        try {
            $userId = Auth::id();
            $tempUserId = $this->getTempUserId($request);

            $cart = $this->cartService->getCart($userId, $tempUserId, $request);

            return response()->json([
                'status' => 'success',
                'data' => [
                    'item_count' => $cart['item_count'],
                    'total_quantity' => $cart['total_quantity'],
                    'subtotal' => $cart['subtotal'],
                    'total' => $cart['total']
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Cart count error: ' . $e->getMessage());
            
            return response()->json([
                'status' => 'success',
                'data' => [
                    'item_count' => 0,
                    'total_quantity' => 0,
                    'subtotal' => 0,
                    'total' => 0
                ]
            ]);
            }
    }
    
    /**
     * Add multiple items to cart (bulk add)
     */
    public function addMultiple(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'items' => 'required|array|min:1|max:20',
                'items.*.product_id' => 'required|integer|exists:products,id',
                'items.*.quantity' => 'integer|min:1|max:999',
                'items.*.variation' => 'nullable|string',
                'items.*.options' => 'nullable|array'
            ]);
        
            if ($validator->fails()) {
            return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $userId = Auth::id();
            $tempUserId = $this->getTempUserId($request);

            $results = [];
            $successCount = 0;
            $errorCount = 0;
            
            foreach ($request->items as $item) {
                $result = $this->cartService->addToCart(
                    $item['product_id'],
                    $item['quantity'] ?? 1,
                    $item['variation'] ?? null,
                    $item['options'] ?? [],
                    $userId,
                    $tempUserId,
                    $request
                );

                $results[] = [
                    'product_id' => $item['product_id'],
                    'success' => $result['success'],
                    'message' => $result['message']
                ];

                if ($result['success']) {
                    $successCount++;
                } else {
                    $errorCount++;
                }
            }

            $cart = $this->cartService->getCart($userId, $tempUserId, $request);

            return response()->json([
                'status' => $errorCount === 0 ? 'success' : ($successCount === 0 ? 'error' : 'partial'),
                'message' => "Added {$successCount} items successfully" . ($errorCount > 0 ? ", {$errorCount} failed" : ""),
                'data' => [
                    'cart' => $cart,
                    'results' => $results,
                    'success_count' => $successCount,
                    'error_count' => $errorCount
                ],
                'temp_user_id' => $tempUserId
            ]);

        } catch (\Exception $e) {
            Log::error('Bulk add to cart error: ' . $e->getMessage());
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to add items to cart',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update cart notes
     */
    public function updateNotes(Request $request): JsonResponse
    {
        try {
        $validator = Validator::make($request->all(), [
                'notes' => 'nullable|string|max:1000'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
            $userId = Auth::id();
            $tempUserId = $this->getTempUserId($request);

            $cartInfo = $this->cartService->getOrCreateCartInfo($userId, $tempUserId, $request);
            $cartInfo->update(['notes' => $request->notes]);

            return response()->json([
                'status' => 'success',
                'message' => 'Cart notes updated successfully',
                'data' => [
                    'notes' => $request->notes
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Update cart notes error: ' . $e->getMessage());
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update cart notes',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get or generate temp user ID
     */
    private function getTempUserId(Request $request): ?string
    {
        // If user is authenticated, don't use temp user ID
        if (Auth::check()) {
            return null;
        }

        return $this->cartService->getTempUserId($request);
    }
} 