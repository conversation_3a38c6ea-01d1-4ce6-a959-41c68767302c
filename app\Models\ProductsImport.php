<?php

namespace App\Models;

use Auth;
use Storage;
use Carbon\Carbon;
use App\Models\User;
use App\Models\Product;
use App\Models\Attribute;
use Illuminate\Support\Str;
use App\Models\ProductStock;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToModel;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

//class ProductsImport implements ToModel, WithHeadingRow, WithValidation
class ProductsImport implements ToCollection, WithHeadingRow, WithValidation, ToModel
{
    private $rows = 0;


    public function collection(Collection $rows)
    {
        //dd($rows);

        $canImport = true;
        $user = Auth::user();
        if ($user->user_type == 'seller' && addon_is_activated('seller_subscription')) {
            if ((count($rows) + $user->products()->count()) > $user->shop->product_upload_limit
                || $user->shop->package_invalid_at == null
                || Carbon::now()->diffInDays(Carbon::parse($user->shop->package_invalid_at), false) < 0
            ) {
                $canImport = false;
                flash(translate('Please upgrade your package.'))->warning();
            }
        }

        if ($canImport) {
            foreach ($rows as $row) {



                $approved = 1;
                if ($user->user_type == 'seller' && get_setting('product_approve_by_admin') == 1) {
                    $approved = 0;
                }


                //date time convert
                $start_date = $row['discount_start_date'];
                $end_date = $row['discount_end_date'];
                $start_timestamp = strtotime($start_date);
                $end_timestamp = strtotime($end_date);
                


                $productId = Product::create([
                    'name' => $row['name'],
                    'description' => $row['description'],
                    'short_description' => $row['short_description'],
                    'added_by' => $user->user_type == 'seller' ? 'seller' : 'admin',
                    'supplier_id' => $row['supplier_id'],
                    'user_id' => $user->user_type == 'seller' ? $user->id : User::where('user_type', 'admin')->first()->id,
                    'approved' => $approved,
                    'published'   => $row['published'],
                    'category_id' => $row['category_id'],
                    'brand_id' => $row['brand_id'],
                    'video_provider' => $row['video_provider'],
                    'video_link' => $row['video_link'],
                    'tags' => $row['tags'],
                    'unit_price' => $row['unit_price'],
                    'dropshipper_price'     => $row['dropshipper_price'],
                    'purchase_price' => $row['purchase_price'],
                    'current_stock' => $row['current_stock'],

                    //for addToCart js problem
                    'choice_options' => json_encode($row['choice_options'] ?? []),
                    'colors'         => json_encode($row['colors'] ?? []),
                    'variations'     => json_encode($row['variations'] ?? []),

                    'stock_visibility_state' => $row['stock_visibility_state'],
                    'cash_on_delivery'  => $row['cash_on_delivery'],
                    'featured'          => $row['featured'],
                    'seller_featured'   => $row['seller_featured'],

                    'unit' => $row['unit'],
                    'weight'    => $row['weight'],
                    'length'    => $row['length'],
                    'width'     => $row['width'],
                    'height'    => $row['height'],
                    'allow_customer_review' => $row['allow_customer_review'],
                    'position'  => $row['position'],
                    'min_qty' => $row['min_qty'],
                    'low_stock_quantity' => $row['low_stock_quantity'],
                    'discount'      => $row['discount'],
                    'discount_type' => $row['discount_type'],

                    'discount_start_date' => $start_timestamp,
                    'discount_end_date'   => $end_timestamp,


                    'tax'      => $row['tax'],
                    'tax_type' => $row['tax_type'],
                    'shipping_type' => $row['shipping_type'],
                    'shipping_cost' => $row['shipping_cost'],
                    'is_quantity_multiplied' => $row['is_quantity_multiplied'],

                    'meta_title' => $row['meta_title'],
                    'meta_description' => $row['meta_description'],
                    // 'meta_img' => $row['meta_img'],
                    'pdf'      => $row['pdf'],
                    'est_shipping_days' => $row['est_shipping_days'],
                    'slug' => preg_replace('/[^A-Za-z0-9\-]/', '', str_replace(' ', '-', strtolower($row['slug']))) . '-' . Str::random(5),
                    'thumbnail_img' => $this->downloadThumbnail($row['thumbnail_img']),
                    'photos' => $this->downloadGalleryImages($row['photos']),
                    'refundable' => $row['refundable'],

                    'auction_product' => $row['auction_product'] ?? 0,
                    
                    // 'external_link'     => $row['external_link'],
                    // 'external_link_btn' => $row['external_link_btn'],
                    'purchase_note' => $row['purchase_note'],
                    'shipping_class' => $row['shipping_class'],
                    'upsell'        => $row['upsell'],
                    'cross_sell'    => $row['cross_sell'],
                    'visibility_in_catalog' => $row['visibility_in_catalog'],
                    'grouped_product'       => $row['grouped_product'],
                    'backorders_allowed'    => $row['backorders_allowed'],
                ]);
                ProductStock::create([
                    'product_id' => $productId->id,
                    'qty' => $row['current_stock'],
                    'price' => $row['unit_price'],
                    'sku' => $row['sku'],
                    'variant' => '',
                ]);
            }

            flash(translate('Products imported successfully'))->success();
        }
    }







    public function model(array $row)
    {
        ++$this->rows;
    }

    public function getRowCount(): int
    {
        return $this->rows;
    }

    public function rules(): array
    {
        return [
            // Can also use callback validation rules
            'unit_price' => function ($attribute, $value, $onFailure) {
                if (!is_numeric($value)) {
                    $onFailure('Unit price is not numeric');
                }
            }
        ];
    }

    public function downloadThumbnail($url)
    {
        try {
            $upload = new Upload;
            $upload->external_link = $url;
            $upload->type = 'image';
            $upload->save();

            return $upload->id;
        } catch (\Exception $e) {
        }
        return null;
    }

    public function downloadGalleryImages($urls)
    {
        $data = array();
        foreach (explode(',', str_replace(' ', '', $urls)) as $url) {
            $data[] = $this->downloadThumbnail($url);
        }
        return implode(',', $data);
    }
}
