<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ActivityLogBk extends Model
{
    protected $table = "activity_log";
    protected $fillable = [
        'log_name',
        'description',
        'source_id',
        'source_type',
        'subject_id',
        'subject_type',
        'causer_id',
        'causer_type',
        'old_status',
        'new_status',
        'reason',
        'batch_uuid',
        'email_end_time',
    ];
    public function user()
    {
        return $this->belongsTo(User::class, 'causer_id');
    }

}
