<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class CartInfo extends Model
{
    protected $table = 'cart_info';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'temp_user_id',
        'address_id',
        'shipping_method_id',
        'item_count',
        'total_quantity',
        'subtotal',
        'discount',
        'coupon_code',
        'shipping_total',
        'tax_total',
        'total',
        'currency'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'subtotal' => 'decimal:2',
        'discount' => 'decimal:2',
        'shipping_total' => 'decimal:2',
        'tax_total' => 'decimal:2',
        'total' => 'decimal:2',
        'item_count' => 'integer',
        'total_quantity' => 'integer',
        'coupon_applied' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Boot function from Laravel.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->{$model->getKeyName()})) {
                $model->{$model->getKeyName()} = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the user that owns the cart.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function carts()
    {
        return $this->hasMany(Cart::class, 'cart_info_id');
    }
    /**
     * Get the value indicating whether the IDs are incrementing.
     *
     * @return bool
     */
    public function getIncrementing()
    {
        return false;
    }

    /**
     * Get the auto-incrementing key type.
     *
     * @return string
     */
    public function getKeyType()
    {
        return 'string';
    }
    public function calculateSubtotal(): float
    {
            if (!$this->carts) {
                return 0.00;
            }
            return (float) $this->carts->sum(function($item) {
                return $item->price * $item->quantity;
            });
    }
    public function total_discount(): float
    {
            if (!$this->carts) {
                return 0.00;
            }
            return (float) $this->carts->sum(function($item) {
                return $item->discount;
            });
    }
    public function tax_total(): float
    {
            if (!$this->carts) {
                return 0.00;
            }
            return (float) $this->carts->sum(function($item) {
                return $item->tax;
            });
    }
    public function total_quantity(): float
    {
            if (!$this->carts) {
                return 0.00;
            }
            return (float) $this->carts->sum(function($item) {
                return $item->quantity;
            });
    }
    public function item_count(): float
    {
            if (!$this->carts) {
                return 0.00;
            }
            return (float) $this->carts->count();
    }
}
