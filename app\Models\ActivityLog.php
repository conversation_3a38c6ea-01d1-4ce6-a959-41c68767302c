<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class ActivityLog extends Model
{
    use HasFactory;

    protected $table = "activity_log";
    protected $fillable = [
        'log_name',
        'source_id',
        'source_type',
        'description',
        'subject_id',
        'subject_type',
        'causer_id',
        'causer_type',
        'old_status',
        'new_status',
        'reason',
        'email_end_time',
    ];

    protected $casts = [
        'details' => 'array',
    ];

    /**
     * Get the subject model of this activity
     */
    public function subject(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the causer model (user or system) that performed this activity
     */
    public function causer(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Create a new activity log for order status changes
     *
     * @param Order $order
     * @param string $oldStatus
     * @param string $newStatus
     * @param int|null $causerId
     * @param string|null $causerType
     * @return ActivityLog
     */
    public static function logOrderStatusChange(Order $order, string $oldStatus, string $newStatus, ?int $causerId = null, ?string $causerType = null): ActivityLog
    {
        $action = 'order_status_changed';
        $description = "Order status changed from " . str_replace('_', ' ', strtolower($oldStatus)) . " to " . str_replace('_', ' ', strtolower($newStatus));

        if ($newStatus === 'ORDER_PLACED') {
            $description = "Order has been placed";
        } else if ($newStatus === 'CONFIRMED') {
            $description = "Order has been confirmed";
        } else if ($newStatus === 'PICKED_UP') {
            $description = "Order has been picked up";
        } else if ($newStatus === 'ON_THE_WAY') {
            $description = "Order is on the way";
        } else if ($newStatus === 'DELIVERED') {
            $description = "Order has been delivered";
        } else if ($newStatus === 'CANCELLED') {
            $description = "Order has been cancelled";
        } else if ($newStatus === 'RETURNED_DELIVERY' || $newStatus === 'RETURN') {
            $description = "Order has been returned";
        }

        return self::create([
            'subject_id' => $order->id,
            'subject_type' => 'App\\Models\\Order',
            'action' => $action,
            'description' => $description,
            'details' => [
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
            ],
            'causer_id' => $causerId,
            'causer_type' => $causerType,
        ]);
    }

    /**
     * Log tracking information update
     *
     * @param Order $order
     * @param string|null $trackingNumber
     * @param string|null $trackingUrl
     * @param int|null $carrierId
     * @param int|null $causerId
     * @param string|null $causerType
     * @return ActivityLog
     */
    public static function logTrackingUpdate(Order $order, ?string $trackingNumber, ?string $trackingUrl, ?int $carrierId, ?int $causerId = null, ?string $causerType = null): ActivityLog
    {
        $action = 'tracking_updated';
        $description = "Tracking information updated";

        if ($trackingNumber && !$order->tracking_number) {
            $description = "Tracking number assigned: " . $trackingNumber;
        }

        $carrier = null;
        if ($carrierId) {
            $carrier = Carrier::find($carrierId);
            if ($carrier) {
                $description .= " with " . $carrier->name;
            }
        }

        return self::create([
            'subject_id' => $order->id,
            'subject_type' => 'App\\Models\\Order',
            'action' => $action,
            'description' => $description,
            'details' => [
                'tracking_number' => $trackingNumber,
                'tracking_url' => $trackingUrl,
                'carrier_id' => $carrierId,
                'carrier_name' => $carrier ? $carrier->name : null,
            ],
            'causer_id' => $causerId,
            'causer_type' => $causerType,
        ]);
    }

    /**
     * Log general order activity
     *
     * @param Order $order
     * @param string $action
     * @param string $description
     * @param array $details
     * @param int|null $causerId
     * @param string|null $causerType
     * @return ActivityLog
     */
    public static function logOrderActivity(Order $order, string $action, string $description, array $details = [], ?int $causerId = null, ?string $causerType = null): ActivityLog
    {
        return self::create([
            'subject_id' => $order->id,
            'subject_type' => 'App\\Models\\Order',
            'action' => $action,
            'description' => $description,
            'details' => $details,
            'causer_id' => $causerId,
            'causer_type' => $causerType,
        ]);
    }

    public function user()
    {
        // Ensure that the causer_type is always 'App\Models\User'
        if ($this->causer_type === 'App\Models\User') {
            return $this->belongsTo(User::class, 'causer_id');
        }

        // If needed, handle other types of causer models or return null for unsupported causer types
        return null;
    }
}
