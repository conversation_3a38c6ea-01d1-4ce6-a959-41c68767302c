<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class TicketCategory extends Model
{
    use HasFactory;
    protected $table = 'ticket_categories';

    protected $fillable = ['name', 'description', 'is_active'];

    /**
     * Get the tickets associated with the category.
     */
    public function tickets()
    {
        return $this->hasMany(Ticket::class, 'ticket_category_id');
    }

    /**
     * Get the active ticket categories.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getActive()
    {
        return self::where('is_active', true)->get();
    }
}
