<?php

namespace App\Http\Resources\V3\ProductStockRequests;

use App\Http\Resources\V3\ProductResource;
use Illuminate\Http\Resources\Json\JsonResource;

class RequestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id'=> $this->id,
            'product'=> new ProductResource($this->product),
            'request_quantity' => $this->quantity,
            'user_name'=> $this->user->name,
            'user_avatar'=> uploaded_asset($this->user->avatar_original),
            'status' => $this->status,
            'note' => $this->note,
            'reviewTime' => $this->updated_at->format('M j, Y \\A\\T H:i')
        ];
    }
}
