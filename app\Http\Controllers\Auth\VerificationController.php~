<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Notifications\OTPEmailVerificationNotification;
use Illuminate\Foundation\Auth\VerifiesEmails;
use App\Models\User;
use App\Models\dropshipperProfile;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Hash;

use App\Http\Controllers\OTPVerificationController;

class VerificationController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Email Verification Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling email verification for any
    | user that recently registered with the application. Emails may also
    | be re-sent if the user didn't receive the original email message.
    |
    */

    use VerifiesEmails;

    /**
     * Where to redirect users after verification.
     *
     * @var string
     */
    protected $redirectTo = '/';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        //$this->middleware('auth');
        $this->middleware('signed')->only('verify');
        $this->middleware('throttle:6,1')->only('verify', 'resend');
    }

    /**
     * Show the email verification notice.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request)
    {
        if ($request->user()->email != null && $request->user()->email_verified_at == null) {
            if($request->user()->user_type == 'guest') {
                return $request->user()->hasVerifiedEmail()
                            ? redirect($this->redirectPath())
                            : view('frontend.user.customer.waiting_for_verification');
            }else {
                return $request->user()->hasVerifiedEmail()
                            ? redirect($this->redirectPath())
                            : view('auth.verify');
            }
        }
        else {
            $otpController = new OTPVerificationController;
            $otpController->send_code($request->user());
            return redirect()->route('verification');
        }
    }


    /**
     * Resend the email verification notification.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function resend(Request $request)
    {
        if ($request->user()->hasVerifiedEmail()) {
            return redirect($this->redirectPath());
        }
        $request->user()->notify(new OTPEmailVerificationNotification());

        return back()->with('resent', true);
    }

    public function verification_confirmation($code){
        $user = User::where('verification_code', $code)->first();
        if($user != null){
            $user->email_verified_at = Carbon::now();
            $user->save();
            auth()->login($user, true);
            flash(translate('Your email has been verified successfully'))->success();
        }
        else {
            flash(translate('Sorry, we could not verifiy you. Please try again'))->error();
        }

        if($user->user_type == 'seller') {
            return redirect()->route('seller.dashboard');
        }
        if($user->user_type == 'guest') {
            return redirect()->route('checkout.shipping_info');
        }
        return redirect()->route('dashboard');
    }

    public function otp_verification_confirmation($code){
        $user = User::where('verification_code', $code)->first();
        if($user != null){
            $user->email_verified_at = Carbon::now();
            $user->verification_code = NULL;
            $user->save();
            auth()->login($user, true);
            flash(translate('Your email has been verified successfully'))->success();
        }
        else {
            flash(translate('Sorry, we could not verifiy you. Please try again'))->error();
        }

        if($user->user_type == 'guest') {
            return redirect()->route('checkout.shipping_info');
        }

        if($user->user_type == 'seller') {
            return redirect()->route('seller.dashboard');
        }

        return redirect()->route('dashboard');
    }

    public function verify_otp_email_reset_password(Request $request){
        // dd($request->all());
        if($request->verification_code){
        }else{
            flash(translate('Sorry, we could not verifiy you. Please try again'))->error();
            return view('dropshipper.waiting_for_verification');
        }
        $code = $request->verification_code;
        //$user = User::where('verification_code', $code)->where('user_type','!=', 'guest')->first();
        $user = User::where('verification_code', $code)->first();
        if (!$user) {
            flash(translate('Invalid verification code'))->error();
            return redirect()->back();
        }

        $userTokenExpiry = Carbon::parse($user->verification_token_expire_at);
        if ($userTokenExpiry->isPast()) {
            flash(translate('Verification Code Expired.Please Click on Resend verification email !'))->error();
            return redirect()->back();
        }

        if($user != null){
            $user->verification_token_expire_at = Null;
            $user->email_verified_at = Carbon::now();
            $user->verification_code = NULL;
            $user->save();
            auth()->login($user, true);
            flash(translate('Your email has been verified successfully'))->success();
            if($user->user_type == 'seller') {
                return redirect()->route('seller.dashboard');
            }
            if($user->user_type == 'dropshipper') {
                //dd('dropshipper');
                $data['shopModel'] = dropshipperProfile::where('user_id', auth()->user()->id )->first();
                return redirect()->route('dropshipper.dashboard', $data);
            }
            if($user->user_type == 'guest') {
                return redirect()->route('checkout.shipping_info');
            }
            return redirect()->route('dashboard');
        }else {
            flash(translate('Sorry, we could not verifiy you. Please try again'))->error();
            // return view('dropshipper.waiting_for_verification');
            return view('dropshipper.waiting_for_verification');

        }


    }

    // dropshipper verify otp redirection


    public function verify_otp_reset_password(Request $request){
        // dd($request->all());
        if($request->verification_code && $request->password){

        }else{
            flash(translate('Sorry, we could not verifiy you. Please try again'))->error();
            return view('emails.waiting_for_verification');
        }
        $code = $request->verification_code;
        $user = User::where('verification_code', $code)->first();
        if($user != null){
            $user->email_verified_at = Carbon::now();
            $user->verification_code = NULL;
            $user->password = Hash::make($request->password);
            $user->save();
            auth()->login($user, true);
            flash(translate('Your reset password has been verified successfully'))->success();
            if($user->user_type == 'seller') {
                return redirect()->route('seller.dashboard');
            }
            if($user->user_type == 'dropshipper') {
                //dd('dropshipper');
                $data['shopModel'] = dropshipperProfile::where('user_id', auth()->user()->id )->first();
                return redirect()->route('dropshipper.dashboard', $data);
            }

            if($user->user_type == 'guest') {
                return redirect()->route('checkout.shipping_info');
            }

            return redirect()->route('dashboard');
        }else {
            flash(translate('Sorry, we could not verifiy you. Please try again'))->error();
            // return view('dropshipper.waiting_for_verification');
            return view('emails.waiting_for_verification');

        }


    }


}
