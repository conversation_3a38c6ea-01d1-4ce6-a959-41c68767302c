<?php

namespace App\Http\Resources\V3\ProductReviewRating;

use Illuminate\Http\Resources\Json\ResourceCollection;

class ProductReviewsResource extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return $this->collection->map(function($review) {
            return new ProductReviewResource($review);
        });
    }
}
