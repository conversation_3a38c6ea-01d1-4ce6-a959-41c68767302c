<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProductStock_1 extends Model
{
    protected $fillable = ['product_id', 'variant', 'sku', 'price', 'dropshipper_price', 'qty', 'image'];
    //
    public function product(){
    	return $this->belongsTo(Product::class);
    }

    public function wholesalePrices() {
        return $this->hasMany(WholesalePrice::class);
    }
}
