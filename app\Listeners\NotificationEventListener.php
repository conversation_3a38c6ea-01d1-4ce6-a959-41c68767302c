<?php

namespace App\Listeners;

use App\Services\NotificationService;
use App\Models\Order;
use App\Models\ReturnRequestInfo;
use App\Models\Product;
use App\Models\User;
use App\Enums\NotificationType;
use App\Enums\NotificationPriority;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class NotificationEventListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Handle order created events
     */
    public function handleOrderCreated($event)
    {
        try {
            $order = $event->order ?? $event;
            $userRole = $order->user->role === 'b2b' ? 'dropshipper' : $order->user->role;
            if ($order instanceof Order) {
                // Notify admin about new order
                $this->notificationService->sendAdminNotification(
                    'New Order Received',
                    "New order #" . $order->code . " has been placed by " . ($order->user->name ?? 'Customer'),
                    NotificationType::ORDER,
                    NotificationPriority::HIGH,
                    "/{$userRole}/orders/" .  $order->code,
                    'View Order',
                    $order,
                    $order->user_id
                );

                // Confirm order placement to customer
                $this->notificationService->sendNotification(
                    $order->user,
                    'Order Placed Successfully',
                    'Your order #' . $order->code . ' has been placed successfully. We will notify you of any updates.',
                    NotificationType::ORDER,
                    NotificationPriority::HIGH,
                    "/{$userRole}/orders/" . $order->code,
                    'View Order',
                    $order
                );
            }
        } catch (\Exception $e) {
            Log::error('Error handling order created event: ' . $e->getMessage());
        }
    }

    /**
     * Handle order status update events
     */
    public function handleOrderStatusUpdate($event)
    {
        try {
            $order = $event->order ?? null;
            $previousStatus = $event->previousStatus ?? $event->previous_status ?? null;
            $newStatus = $event->newStatus ?? $event->new_status ?? $order->status ?? null;

            if ($order instanceof Order && $previousStatus && $newStatus && $previousStatus !== $newStatus) {
                $this->notificationService->sendOrderStatusUpdate($order, $previousStatus, $newStatus);
            }
        } catch (\Exception $e) {
            Log::error('Error handling order status update event: ' . $e->getMessage());
        }
    }

    /**
     * Handle return request created events
     */
    public function handleReturnRequestCreated($event)
    {
        try {
            $returnRequest = $event->returnRequest ?? $event;
            
            if ($returnRequest instanceof ReturnRequestInfo) {
                $this->notificationService->sendReturnRequestNotification($returnRequest, 'created', true);
            }
        } catch (\Exception $e) {
            Log::error('Error handling return request created event: ' . $e->getMessage());
        }
    }

    /**
     * Handle return request status update events
     */
    public function handleReturnRequestStatusUpdate($event)
    {
        try {
            $returnRequest = $event->returnRequest ?? $event;
            $action = $event->action ?? $event->status ?? 'updated';
            
            if ($returnRequest instanceof ReturnRequestInfo) {
                // Notify customer about status change
                $this->notificationService->sendReturnRequestNotification($returnRequest, $action, false);
            }
        } catch (\Exception $e) {
            Log::error('Error handling return request status update event: ' . $e->getMessage());
        }
    }

    /**
     * Handle user registration events
     */
    public function handleUserRegistered($event)
    {
        try {
            $user = $event->user ?? $event;
            
            if ($user instanceof User) {
                // Send welcome notification to user
                $this->notificationService->sendNotification(
                    $user,
                    'Welcome to Buzfi!',
                    'Thank you for joining us. Explore our products and enjoy shopping with us.',
                    NotificationType::SYSTEM,
                    NotificationPriority::MEDIUM,
                    "/products",
                    'Browse Products'
                );

                // Notify admin about new user registration
                $this->notificationService->sendAdminNotification(
                    'New User Registration',
                    "New user " . $user->name . " has registered on the platform.",
                    NotificationType::SYSTEM,
                    NotificationPriority::LOW,
                    "/admin/users/" . $user->id,
                    'View User',
                    $user,
                    $user->id
                );
            }
        } catch (\Exception $e) {
            Log::error('Error handling user registered event: ' . $e->getMessage());
        }
    }

    /**
     * Handle product stock low events
     */
    public function handleProductStockLow($event)
    {
        try {
            $product = $event->product ?? $event;
            
            if ($product instanceof Product) {
                // Notify admin about low stock
                $this->notificationService->sendAdminNotification(
                    'Low Stock Alert',
                    "Product '" . $product->name . "' is running low on stock (Current: " . ($product->stock_quantity ?? 0) . ")",
                    NotificationType::STOCK,
                    NotificationPriority::HIGH,
                    "/admin/products/" . $product->id,
                    'Manage Stock',
                    $product
                );
            }
        } catch (\Exception $e) {
            Log::error('Error handling product stock low event: ' . $e->getMessage());
        }
    }

    /**
     * Handle payment successful events
     */
    public function handlePaymentSuccessful($event)
    {
        try {
            $order = $event->order ?? null;
            $payment = $event->payment ?? $event;
            
            if ($order instanceof Order) {
                $userRole = $order->user->role === 'b2b' ? 'dropshipper' : $order->user->role;
                // Notify customer about successful payment
                $this->notificationService->sendNotification(
                    $order->user,
                    'Payment Confirmed',
                    'Your payment for order #' . $order->code . ' has been successfully processed.',
                    NotificationType::ORDER,
                    NotificationPriority::HIGH,
                    "/{$userRole}/orders/" . $order->code,
                    'View Order',
                    $order
                );

                // Notify admin about payment
                $this->notificationService->sendAdminNotification(
                    'Payment Received',
                    'Payment for order #' . $order->code. ' has been successfully processed.',
                    NotificationType::ORDER,
                    NotificationPriority::MEDIUM,
                    "/{$userRole}/orders/" . $order->code,
                    'View Order',
                    $order,
                    $order->user_id
                );
            }
        } catch (\Exception $e) {
            Log::error('Error handling payment successful event: ' . $e->getMessage());
        }
    }

    /**
     * Handle payment failed events
     */
    public function handlePaymentFailed($event)
    {
        try {
            $order = $event->order ?? null;
            $payment = $event->payment ?? $event;
            
            if ($order instanceof Order) {
                $userRole = $order->user->role === 'b2b' ? 'dropshipper' : $order->user->role;
                // Notify customer about failed payment
                $this->notificationService->sendNotification(
                    $order->user,
                    'Payment Failed',
                    'Your payment for order #' . $order->code . ' could not be processed. Please try again.',
                    NotificationType::ORDER,
                    NotificationPriority::HIGH,
                    "/{$userRole}/orders/" . $order->code . "/payment",
                    'Retry Payment',
                    $order
                );
            }
        } catch (\Exception $e) {
            Log::error('Error handling payment failed event: ' . $e->getMessage());
        }
    }

    /**
     * Handle support ticket created events
     */
    public function handleSupportTicketCreated($event)
    {
        try {
            $ticket = $event->ticket ?? $event;
            
            // Notify admin about new support ticket
            $this->notificationService->sendAdminNotification(
                'New Support Ticket',
                'A new support ticket has been created: ' . ($ticket->subject ?? 'Support Request'),
                NotificationType::SUPPORT,
                NotificationPriority::MEDIUM,
                "/admin/support/tickets/" . ($ticket->id ?? ''),
                'View Ticket',
                $ticket,
                $ticket->user_id ?? null
            );
        } catch (\Exception $e) {
            Log::error('Error handling support ticket created event: ' . $e->getMessage());
        }
    }

    /**
     * Handle message sent events
     */
    public function handleMessageSent($event)
    {
        try {
            $message = $event->message ?? [];
            $messageType = $event->messageType ?? $event->message_type ?? 'general';
            $recipientIds = $event->recipientIds ?? $event->recipient_ids ?? [];
            $notifyAdmin = $event->notifyAdmin ?? $event->notify_admin ?? false;

            $this->notificationService->sendMessageNotification(
                $message,
                $messageType,
                $recipientIds,
                $notifyAdmin
            );
        } catch (\Exception $e) {
            Log::error('Error handling message sent event: ' . $e->getMessage());
        }
    }

    /**
     * Handle product review submitted events
     */
    public function handleProductReviewSubmitted($event)
    {
        try {
            $review = $event->review ?? $event;
            $product = $review->product ?? null;
            
            if ($product) {
                // Notify admin about new review
                $this->notificationService->sendAdminNotification(
                    'New Product Review',
                    'A new review has been submitted for product: ' . $product->name,
                    NotificationType::PRODUCT,
                    NotificationPriority::LOW,
                    "/admin/products/" . $product->id . "/reviews",
                    'View Review',
                    $review,
                    $review->user_id ?? null
                );
            }
        } catch (\Exception $e) {
            Log::error('Error handling product review submitted event: ' . $e->getMessage());
        }
    }

    /**
     * Handle commission earned events
     */
    public function handleCommissionEarned($event)
    {
        try {
            $commission = $event->commission ?? $event;
            $user = $commission->user ?? null;
            
            if ($user) {
                // Notify user about earned commission
                $amount = $commission->amount ?? 0;
                $this->notificationService->sendNotification(
                    $user,
                    'Commission Earned',
                    'You have earned a commission of $' . number_format($amount, 2) . ' from your referral.',
                    NotificationType::COMMISSION,
                    NotificationPriority::MEDIUM,
                    "/dashboard/commissions",
                    'View Commissions',
                    $commission
                );
            }
        } catch (\Exception $e) {
            Log::error('Error handling commission earned event: ' . $e->getMessage());
        }
    }

    /**
     * Handle wishlist item back in stock events
     */
    public function handleWishlistItemBackInStock($event)
    {
        try {
            $product = $event->product ?? $event;
            $users = $event->users ?? [];
            
            if ($product instanceof Product && !empty($users)) {
                foreach ($users as $user) {
                    $this->notificationService->sendNotification(
                        $user,
                        'Product Back in Stock',
                        'Good news! "' . $product->name . '" from your wishlist is now back in stock.',
                        NotificationType::STOCK,
                        NotificationPriority::MEDIUM,
                        "/products/" . $product->id,
                        'View Product',
                        $product
                    );
                }
            }
        } catch (\Exception $e) {
            Log::error('Error handling wishlist item back in stock event: ' . $e->getMessage());
        }
    }

    /**
     * Handle general events
     */
    public function handle($event)
    {
        $eventClass = get_class($event);
        $method = $this->getHandlerMethod($eventClass);
        
        if (method_exists($this, $method)) {
            $this->$method($event);
        }
    }

    /**
     * Get the handler method name for an event class
     */
    private function getHandlerMethod($eventClass)
    {
        $className = class_basename($eventClass);
        
        // Convert PascalCase to camelCase and add 'handle' prefix
        $method = 'handle' . $className;
        
        return $method;
    }
} 