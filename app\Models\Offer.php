<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

class Offer extends Model
{
    protected $table = 'offers';

    protected $fillable = [
        'offer_id',
        'title',
        'description',
        'offer_banner',
        'discount_type',
        'discount_percentage',
        'discount_amount',
        'min_order_value',
        'promo_code',
        'start_date',
        'end_date',
        'status',
        'is_exclusive',
        'is_seasonal',
        'is_personalized',
        'usage_limit',
        'used_count',
        'is_redeemed',
        'is_expired',
        'priority',
        'tags',
        'terms_conditions',
        'is_dropshipper_only',
        'user_type',
        'bulk_discounts',
        'savings_amount',
        'created_by',
        'updated_by'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'discount_percentage' => 'float',
        'discount_amount' => 'float',
        'min_order_value' => 'float',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'is_exclusive' => 'boolean',
        'is_seasonal' => 'boolean',
        'is_personalized' => 'boolean',
        'usage_limit' => 'integer',
        'used_count' => 'integer',
        'is_redeemed' => 'boolean',
        'is_expired' => 'boolean',
        'priority' => 'integer',
        'tags' => 'json',
        'terms_conditions' => 'json',
        'is_dropshipper_only' => 'boolean',
        'bulk_discounts' => 'json',
        'savings_amount' => 'float'
    ];

    /**
     * Get the categories associated with this offer
     */
    public function categories()
    {
        return $this->belongsToMany(Category::class, 'offer_category', 'offer_id', 'category_id');
    }

    /**
     * Get the products associated with this offer
     */
    public function products()
    {
        return $this->belongsToMany(Product::class, 'offer_product', 'offer_id', 'product_id');
    }

    /**
     * Get user offer usages for this offer
     */
    public function userOfferUsages()
    {
        return $this->hasMany(UserOfferUsage::class);
    }

    /**
     * Get categories attribute - ensures it's always a collection
     */
    public function getCategoriesAttribute($value)
    {
        if (!$this->relationLoaded('categories')) {
            $this->load('categories');
        }
        return $this->getRelation('categories') ?? collect([]);
    }

    /**
     * Get products attribute - ensures it's always a collection
     */
    public function getProductsAttribute($value)
    {
        if (!$this->relationLoaded('products')) {
            $this->load('products');
        }
        return $this->getRelation('products') ?? collect([]);
    }

    /**
     * Get the user who created this offer
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this offer
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Check if offer is active based on dates and status
     *
     * @return bool
     */
    public function isActive()
    {
        $now = Carbon::now();
        return $now->greaterThanOrEqualTo($this->start_date) &&
               $now->lessThanOrEqualTo($this->end_date) &&
               $this->status === 'active';
    }

    /**
     * Check if offer is expiring soon (within 7 days)
     *
     * @param int $days
     * @return bool
     */
    public function isExpiringSoon($days = 7)
    {
        $now = Carbon::now();
        $expiresAt = Carbon::parse($this->end_date);
        
        return $now->lt($expiresAt) && 
               $now->diffInDays($expiresAt) <= $days;
    }

    /**
     * Get formatted expiration time
     * 
     * @return string
     */
    public function getExpirationTimeAttribute()
    {
        $now = Carbon::now();
        $expiresAt = Carbon::parse($this->end_date);
        
        if ($expiresAt->lt($now)) {
            return "Expired";
        }
        
        return "Expires " . $expiresAt->diffForHumans();
    }

    /**
     * Get the time remaining until the offer expires
     * 
     * @return string
     */
    public function getTimeRemainingAttribute()
    {
        $now = Carbon::now();
        $expiresAt = Carbon::parse($this->end_date);
        
        if ($expiresAt->lt($now)) {
            return "Expired";
        }
        
        $diff = $now->diff($expiresAt);
        
        if ($diff->days > 0) {
            return $diff->days . " days";
        } elseif ($diff->h > 0) {
            return $diff->h . " hours";
        } else {
            return $diff->i . " minutes";
        }
    }

    /**
     * Increment the used count
     * 
     * @return void
     */
    public function incrementUsedCount()
    {
        $this->used_count++;
        
        if ($this->usage_limit && $this->used_count >= $this->usage_limit) {
            $this->is_redeemed = true;
            $this->status = 'expired';
        }
        
        $this->save();
    }

    /**
     * Check if the offer is applicable for a specific user type
     * 
     * @param string $type
     * @return bool
     */
    public function isApplicableForUserType($type)
    {
        return $this->user_type === 'all' || $this->user_type === $type;
    }

    /**
     * Apply bulk discount based on quantity
     * 
     * @param int $quantity
     * @param float $originalDiscount
     * @return float
     */
    public function getEffectiveDiscount($quantity, $originalDiscount)
    {
        $bulkDiscounts = $this->bulk_discounts ?? [];
        $additionalDiscount = 0;

        if (!empty($bulkDiscounts) && is_array($bulkDiscounts)) {
            foreach ($bulkDiscounts as $discount) {
                if ($quantity >= $discount['quantity']) {
                    // Find the maximum applicable additional discount
                    $additionalDiscount = max($additionalDiscount, (float)$discount['additional_discount']);
                }
            }
        }

        return $originalDiscount + ($additionalDiscount / 100) * $originalDiscount;
    }

    /**
     * Check if user has reached usage limit for this offer
     */
    public function hasUserReachedLimit(int $userId): bool
    {
        $userUsage = $this->userOfferUsages()->where('user_id', $userId)->first();
        return $userUsage && $userUsage->hasReachedLimit();
    }

    /**
     * Get user's usage for this offer
     */
    public function getUserUsage(int $userId): ?UserOfferUsage
    {
        return $this->userOfferUsages()->where('user_id', $userId)->first();
    }

    /**
     * Check if offer can be applied by user
     */
    public function canBeAppliedByUser(int $userId): bool
    {
        if (!$this->isActive()) {
            return false;
        }

        if ($this->hasUserReachedLimit($userId)) {
            return false;
        }

        return true;
    }
} 