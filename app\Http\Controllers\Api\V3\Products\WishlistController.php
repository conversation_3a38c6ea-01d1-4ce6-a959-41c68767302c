<?php

namespace App\Http\Controllers\Api\V3\Products;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Controllers\Api\V3\ApiCartController;
use App\Http\Resources\V3\WishListResource;
use App\Models\Wishlist;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Product;
use App\Models\Cart;
use App\Services\CartService;

class WishlistController extends ApiResponse
{
    public function index(Request $request)
    {
        // Handle both parameter names for pagination
        $per_page = (int) $request->input('per_page', $request->input('perPage', 12));
        $page = (int) $request->input('page', 1);

        // Handle sorting parameters
        $sort = $request->input('sort', $request->input('sortBy', 'created_at'));
        $sortDirection = $request->input('sortDirection', $request->input('sortOrder', 'desc'));

        // Handle search parameter
        $search = $request->input('search', '');

        //$user = auth()->user();
        $user_id =  auth('sanctum')->id();;


        // Build base query with filters
        $baseQuery = Wishlist::where('user_id', $user_id);

        // Apply search filter if provided
        if (!empty($search)) {
            $baseQuery->whereHas('product', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }

        // Get total count before applying pagination and joins
        $total_items = $baseQuery->count();

        // Build query for actual data with sorting
        $query = Wishlist::where('user_id', $user_id)->with('product');

        // Re-apply search filter to data query
        if (!empty($search)) {
            $query->whereHas('product', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        if ($sort === 'addedAt' || $sort === 'created_at') {
            $query->orderBy('wishlists.created_at', $sortDirection);
        } elseif ($sort === 'price') {
            $query->join('products', 'wishlists.product_id', '=', 'products.id')
                  ->orderBy('products.unit_price', $sortDirection)
                  ->select('wishlists.*');
        } elseif ($sort === 'name') {
            $query->join('products', 'wishlists.product_id', '=', 'products.id')
                  ->orderBy('products.name', $sortDirection)
                  ->select('wishlists.*');
        } else {
            // Default sorting by created_at
            $query->orderBy('wishlists.created_at', $sortDirection);
        }

        // Apply pagination
        $wishList = $query->skip(($page - 1) * $per_page)->take($per_page)->get();

        $total_pages = ceil($total_items / $per_page);

        // Build proper response structure that matches frontend expectations
        $data = [
            'wishlist' => new WishListResource($wishList),
            'pagination' => [
                'currentPage' => $page,
                'totalPages' => $total_pages,
                'totalItems' => $total_items,
                'itemsPerPage' => $per_page,
            ]
        ];

        return $this->success($data, 'Wishlist retrieved successfully');
    }

    public function store(Request $request)
    {
        $messages = array(
            'product_id.required' => translate('Product is required'),
        );
        $validator = Validator::make($request->all(), [
            'product_id' => 'required',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }
        $product=Product::where('slug', $request->product_id)->firstOrFail();

        try {
            $user = auth()->user();
            $wishlist = Wishlist::where('user_id', $user->id)->where('product_id', $product->id)->first();
            if (!$wishlist) {
                $wishlist = new Wishlist;
                $wishlist->user_id = $user->id;
                $wishlist->product_id = $product->id;
                $wishlist->save();
            }

            // Return the wishlist item ID for reference
            return $this->success([
                'wishlistItemId' => $wishlist->id,
                'productId' => $request->product_id,
                'inWishlist' => true
            ], 'Product added to wishlist successfully');
        } catch (\Throwable $th) {
            return $this->error(400, 'Failed to add product to wishlist', $th->getMessage());
        }
    }

    public function delete(Request $request)
    {
        $messages = array(
            'wishlist_item_id.required_without' => translate('Wishlist item ID or Product ID is required'),
            'product_id.required_without' => translate('Wishlist item ID or Product ID is required'),
        );
        $validator = Validator::make($request->all(), [
            'wishlist_item_id' => 'required_without:product_id',
            'product_id' => 'required_without:wishlist_item_id',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }

        try {
            $user = auth()->user();

            // Handle removal by wishlist_item_id (preferred method)
            if ($request->has('wishlist_item_id') && $request->wishlist_item_id) {
                $wishlist = Wishlist::where('user_id', $user->id)->where('id', $request->wishlist_item_id)->first();
            }
            // Handle removal by product_id (fallback method for backward compatibility)
            elseif ($request->has('product_id') && $request->product_id) {
                $product=Product::where('slug', $request->product_id)->firstOrFail();
                $wishlist = Wishlist::where('user_id', $user->id)->where('product_id', $product->id)->first();
            }
            else {
                return $this->error(400, 'Failed to remove product from wishlist', 'Neither wishlist_item_id nor product_id provided');
            }

            if ($wishlist) {
                $productId = $wishlist->product_id; // Store for response
                $wishlist->delete();

                return $this->success([
                    'inWishlist' => false,
                    'productId' => $productId,
                    'wishlistItemId' => null
                ], 'Product removed from wishlist successfully');
            } else {
                return $this->success([
                    'inWishlist' => false,
                    'productId' => $request->product_id ?? null,
                    'wishlistItemId' => null
                ], 'Product was not in wishlist');
            }
        } catch (\Throwable $th) {
            return $this->error(400, 'Failed to remove product from wishlist', $th->getMessage());
        }
    }

    public function deleteAll(Request $request)
    {
        try {
            $user = auth()->user();
            $wishlist = Wishlist::where('user_id', $user->id)->delete();
            return $this->success([
                'totalItems' => 0
            ], 'All products removed from wishlist successfully');
        } catch (\Throwable $th) {
            return $this->error(400, 'Failed to remove product to wishlist', $th->getMessage());
        }
    }

    public function checkProductInWishlist(Request $request)
    {
        $messages = array(
            'product_id.required' => translate('Product is required'),
        );
        $validator = Validator::make($request->all(), [
            'product_id' => 'required',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }

        try {
            $user = auth()->user();
            $wishlist = Wishlist::where('user_id', $user->id)->where('product_id', $request->product_id)->first();

            if (!$wishlist) {
                return $this->success(
                    [
                        'inWishlist' => false,
                        'productId' => $request->product_id,
                        'wishlistItemId' => null
                    ],
                    'Product is not in wishlist'
                );
            }
            return $this->success(
                [
                    'inWishlist' => true,
                    'productId' => $request->product_id,
                    'wishlistItemId' => $wishlist->id
                ],
                'Product is in wishlist'
            );
        } catch (\Throwable $th) {
            return $this->error(400, 'Failed to check product in wishlist', $th->getMessage());
        }
    }

    /**
     * Get statistics about the user's wishlist
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats()
    {
        try {
            $user = auth()->user();

            // Get all wishlist items with their products
            $wishlistItems = Wishlist::where('user_id', $user->id)
                ->with('product')
                ->get();

            // Calculate total items
            $totalItems = $wishlistItems->count();

            if ($totalItems === 0) {
                return $this->success([
                    'totalItems' => 0,
                    'totalValue' => 0,
                    'averagePrice' => 0,
                    'categoryCounts' => [],
                    'onSaleCount' => 0,
                    'outOfStockCount' => 0
                ]);
            }

            // Calculate total value and prepare for other stats
            $totalValue = 0;
            $categoryCounts = [];
            $onSaleCount = 0;
            $outOfStockCount = 0;

            foreach ($wishlistItems as $item) {
                if (!$item->product) {
                    continue; // Skip if product doesn't exist
                }

                // Add to total value (use discounted price if available)
                $price = $item->product->discount > 0 ?
                    $item->product->unit_price - ($item->product->unit_price * $item->product->discount / 100) :
                    $item->product->unit_price;
                $totalValue += $price;

                // Count by category
                if ($item->product->category) {
                    $categoryName = $item->product->category->name;
                    if (!isset($categoryCounts[$categoryName])) {
                        $categoryCounts[$categoryName] = 0;
                    }
                    $categoryCounts[$categoryName]++;
                }

                // Check if on sale
                if ($item->product->discount > 0) {
                    $onSaleCount++;
                }

                // Check if out of stock
                if ($item->product->current_stock <= 0) {
                    $outOfStockCount++;
                }
            }

            // Calculate average price
            $averagePrice = $totalValue / $totalItems;

            // Format the values to 2 decimal places
            $totalValue = round($totalValue, 2);
            $averagePrice = round($averagePrice, 2);

            return $this->success([
                'totalItems' => $totalItems,
                'totalValue' => $totalValue,
                'averagePrice' => $averagePrice,
                'categoryCounts' => $categoryCounts,
                'onSaleCount' => $onSaleCount,
                'outOfStockCount' => $outOfStockCount
            ]);

        } catch (\Throwable $th) {
            return $this->error(400, 'Failed to retrieve wishlist statistics', $th->getMessage());
        }
    }

    /**
     * Move wishlist items to cart
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function moveToCart(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'items' => 'required|array',
            'items.*.wishlistItemId' => 'required|string',
            'items.*.quantity' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid data', $validator->errors()->messages(), 400);
        }

        try {
            $user = auth()->user();
            $items = $request->items;
            $itemsAdded = 0;
            $itemsWithIssues = [];

            // Debug logging - log request details
            \Log::info('MoveToCart Debug - User ID: ' . $user->id);
            \Log::info('MoveToCart Debug - Request items: ' . json_encode($items));

            // Debug - check what wishlist items exist for this user
            $allUserWishlistItems = Wishlist::where('user_id', $user->id)->pluck('id')->toArray();
            \Log::info('MoveToCart Debug - All user wishlist IDs: ' . json_encode($allUserWishlistItems));

            // Initialize cart controller with CartService
            $cartService = app(CartService::class);
            $cartController = new ApiCartController($cartService);

            foreach ($items as $item) {
                \Log::info('MoveToCart Debug - Looking for wishlist item ID: ' . $item['wishlistItemId'] . ' (type: ' . gettype($item['wishlistItemId']) . ')');

                // Find wishlist item by ID - FIXED: Use 'id' field instead of 'product_id'
                $wishlistItem = Wishlist::where('user_id', $user->id)
                    ->where('product_id', $item['wishlistItemId'])
                    ->with('product')
                    ->first();

                \Log::info('MoveToCart Debug - Wishlist item found: ' . ($wishlistItem ? 'Yes (ID: ' . $wishlistItem->id . ', Product ID: ' . $wishlistItem->product_id . ')' : 'No'));

                if (!$wishlistItem || !$wishlistItem->product) {
                    $itemsWithIssues[] = [
                        'wishlistItemId' => $item['wishlistItemId'],
                        'issue' => 'Product not found in wishlist'
                    ];
                    continue;
                }

                // Get the product from wishlist item
                $product = $wishlistItem->product;

                // Check if product is available
                if (!$product->published) {
                    $itemsWithIssues[] = [
                        'wishlistItemId' => $item['wishlistItemId'],
                        'issue' => 'Product is no longer available'
                    ];
                    continue;
                }

                // Create request for addToCart function
                $addToCartRequest = new Request([
                    'product_id' => $product->id,
                    'quantity' => $item['quantity'],
                    'options' => [] // No variations for wishlist items
                ]);

                // Copy headers from original request for cart functionality
                $addToCartRequest->headers->set('X-Temp-User-Id', $request->header('X-Temp-User-Id'));
                $addToCartRequest->headers->set('X-Cart-Id', $request->header('X-Cart-Id'));

                try {
                    // Use the addToCart function from ApiCartController
                    $addToCartResponse = $cartController->addToCart($addToCartRequest);
                    $responseData = json_decode($addToCartResponse->getContent(), true);

                    if ($responseData['status'] === 'success') {
                        // Successfully added to cart, remove from wishlist
                        // $wishlistItem->delete();
                        $itemsAdded++;

                        \Log::info('MoveToCart Debug - Successfully moved item to cart and removed from wishlist: ' . $item['wishlistItemId']);
                    } else {
                        // Failed to add to cart
                        $itemsWithIssues[] = [
                            'wishlistItemId' => $item['wishlistItemId'],
                            'issue' => $responseData['error']['message'] ?? 'Failed to add to cart'
                        ];

                        \Log::info('MoveToCart Debug - Failed to add to cart: ' . $item['wishlistItemId'] . ' - ' . ($responseData['error']['message'] ?? 'Unknown error'));
                    }
                } catch (\Exception $e) {
                    $itemsWithIssues[] = [
                        'wishlistItemId' => $item['wishlistItemId'],
                        'issue' => 'Error adding to cart: ' . $e->getMessage()
                    ];

                    \Log::error('MoveToCart Debug - Exception adding to cart: ' . $e->getMessage());
                }
            }

            // Return response based on results
            if ($itemsAdded > 0) {
                return $this->success([
                    'itemsAdded' => $itemsAdded,
                    'itemsWithIssues' => $itemsWithIssues
                ], "$itemsAdded item(s) moved to cart successfully");
            } else {
                return $this->error(400, 'Failed to move items to cart', json_encode($itemsWithIssues));
            }

        } catch (\Throwable $th) {
            \Log::error('MoveToCart Debug - General exception: ' . $th->getMessage());
            return $this->error(400, 'Failed to move items to cart', $th->getMessage());
        }
    }
}
