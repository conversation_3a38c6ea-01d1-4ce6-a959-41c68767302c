<?php

namespace App\Http\Controllers\Api\V3\Upload;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class ApiAvatarUploadController extends Controller
{
    /**
     * Test endpoint to verify routing is working
     */
    public function test()
    {
        return response()->json([
            'success' => true,
            'message' => 'Avatar upload controller is working!',
            'timestamp' => now()->toDateTimeString(),
            'php_version' => phpversion(),
        ]);
    }

    /**
     * Upload avatar using base64 data
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadAvatar(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'avatar' => 'required|string', // Base64 encoded image
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please provide valid information',
                    'errors' => $validator->errors()->messages(),
                ], 400);
            }

            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated',
                ], 401);
            }
            
            $avatarData = $request->avatar;
            
            // Check if it's a base64 image
            if (!preg_match('/^data:image\/(\w+);base64,/', $avatarData, $matches)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid image format. Image must be base64 encoded',
                ], 400);
            }
            
            $imageType = $matches[1];
            $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            
            if (!in_array(strtolower($imageType), $allowedTypes)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid image type. Allowed types: jpg, jpeg, png, gif, webp',
                ], 400);
            }
            
            // Extract base64 data
            $avatarData = substr($avatarData, strpos($avatarData, ',') + 1);
            $avatarData = base64_decode($avatarData);
            
            if ($avatarData === false) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to decode base64 image',
                ], 400);
            }
            
            // Check file size (max 5MB)
            $maxSize = 5 * 1024 * 1024; // 5MB in bytes
            if (strlen($avatarData) > $maxSize) {
                return response()->json([
                    'success' => false,
                    'message' => 'Image size must not exceed 5MB',
                ], 400);
            }
            
            // Delete old avatar if exists
            if ($user->avatar_original) {
                $oldPath = str_replace(asset('storage/'), '', $user->avatar_original);
                try {
                    Storage::disk('public')->delete($oldPath);
                } catch (\Exception $e) {
                    // Continue even if deletion fails
                }
            }
            
            // Generate unique filename
            $fileName = 'avatar_' . $user->id . '_' . time() . '.' . $imageType;
            $path = 'uploads/avatars/' . date('Y/m/d') . '/' . $fileName;
            
            // Store the file
            Storage::disk('public')->put($path, $avatarData);
            
            // Update user avatar
            $avatarUrl = asset('storage/' . $path);
            $user->avatar_original = $avatarUrl;
            $user->save();
            
            return response()->json([
                'success' => true,
                'message' => 'Avatar updated successfully',
                'data' => [
                    'avatar_url' => $avatarUrl,
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'avatar_original' => $user->avatar_original,
                    ]
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload avatar: ' . $e->getMessage(),
            ], 500);
        }
    }
    
    /**
     * Remove avatar
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function removeAvatar(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated',
                ], 401);
            }
            
            // Delete avatar file if exists
            if ($user->avatar_original) {
                $oldPath = str_replace(asset('storage/'), '', $user->avatar_original);
                try {
                    Storage::disk('public')->delete($oldPath);
                } catch (\Exception $e) {
                    // Continue even if deletion fails
                }
            }
            
            // Remove avatar from user record
            $user->avatar_original = null;
            $user->save();
            
            return response()->json([
                'success' => true,
                'message' => 'Avatar removed successfully',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'avatar_original' => null,
                    ]
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove avatar: ' . $e->getMessage(),
            ], 500);
        }
    }
    
    /**
     * Get current avatar
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvatar(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated',
                ], 401);
            }
            
            return response()->json([
                'success' => true,
                'message' => 'Avatar retrieved successfully',
                'data' => [
                    'avatar_url' => $user->avatar_original,
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'avatar_original' => $user->avatar_original,
                    ]
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve avatar: ' . $e->getMessage(),
            ], 500);
        }
    }
} 