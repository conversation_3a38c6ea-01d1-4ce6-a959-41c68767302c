<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CustomNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $notificationData;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($notificationData)
    {
        $this->notificationData = $notificationData;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject($this->notificationData['title'])
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line($this->notificationData['message'])
            ->when($this->notificationData['link'], function ($mail) {
                return $mail->action(
                    $this->notificationData['link_text'] ?? 'View Details',
                    $this->notificationData['link']
                );
            })
            ->line('Thank you for using Buzfi!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'id' => $this->id ?? uniqid(),
            'title' => $this->notificationData['title'],
            'message' => $this->notificationData['message'],
            'type' => $this->notificationData['type'],
            'priority' => $this->notificationData['priority'],
            'link' => $this->notificationData['link'] ?? null,
            'link_text' => $this->notificationData['link_text'] ?? null,
            'admin_id' => $this->notificationData['admin_id'],
            'sent_at' => $this->notificationData['sent_at'],
            'subject_type' => 'custom_admin_notification',
            'subject_id' => null,
        ];
    }

    /**
     * Get the broadcastable representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toBroadcast($notifiable)
    {
        return [
            'notification' => $this->toArray($notifiable),
            'user_id' => $notifiable->id,
        ];
    }
} 