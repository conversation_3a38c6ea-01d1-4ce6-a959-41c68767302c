<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class RestrictedOldView
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Add your restriction logic here
        // Example: If the user is not logged in, redirect to login page
        if (!auth()->check()) {
            // Redirect to login page if the user is not logged in
            return redirect()->route('login');
        }

        // If the user is logged in, allow the request to continue
        return $next($request);
    }
}
