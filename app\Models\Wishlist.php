<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Wishlist extends Model
{
    protected $guarded = [];
    protected $fillable = ['user_id','product_id'];

    public function product()
    {
        return $this->belongsTo(Product::class)->withDefault([
            'name' => 'Product Not Available',
            'thumbnail_img' => null,
            'unit_price' => 0,
            'slug' => null,
            'dropshipper_price' => 0,
        ]);
    }
    
    // Add this method to get product details even if relationship is null
    public function getProductDetailsAttribute()
    {
        if ($this->product) {
            return [
                'name' => $this->product->name,
                'price' => $this->product->unit_price,
                'thumbnail_img' => uploaded_asset($this->product->thumbnail_img),
                'slug' => $this->product->slug,
                'dropshipper_price' => $this->product->dropshipper_price
            ];
        }
        
        return [
            'name' => 'Product Not Available',
            'price' => 0,
            'thumbnail_img' => null,
            'slug' => null,
            'dropshipper_price' => 0
        ];
    }
}