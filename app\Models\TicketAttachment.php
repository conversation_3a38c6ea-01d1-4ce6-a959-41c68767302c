<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TicketAttachment extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'ticket_id',
        'message_id',
        'upload_id',
        'file_name',
        'file_path',
        'file_size',
        'file_type',
        'uploaded_by'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'file_size' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * Get the ticket that owns the attachment.
     */
    public function ticket(): BelongsTo
    {
        return $this->belongsTo(SupportTicket::class, 'ticket_id');
    }

    /**
     * Get the message that owns the attachment.
     */
    public function message(): BelongsTo
    {
        return $this->belongsTo(TicketMessage::class, 'message_id');
    }

    /**
     * Get the user that uploaded the attachment.
     */
    public function uploader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get the file URL attribute using uploaded_asset function.
     * Uses the upload_id to get the proper file URL.
     */
    public function getFileUrlAttribute()
    {
        if ($this->upload_id) {
            return uploaded_asset($this->upload_id);
        }
        
        // Fallback for old attachments without upload_id
        if ($this->file_path) {
            $baseUrl = url('/');
            
            // If file_path already starts with /storage, use it directly
            if (str_starts_with($this->file_path, '/storage/')) {
                return $baseUrl . '/public' . $this->file_path;
            }
            
            // If file_path doesn't start with /storage, assume it's a relative path
            return $baseUrl . '/public/storage/' . ltrim($this->file_path, '/');
        }
        
        return null;
    }
} 