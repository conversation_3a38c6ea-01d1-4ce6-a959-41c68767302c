<?php

namespace App\Enums;

class NotificationPriority
{
    const LOW = 'low';
    const MEDIUM = 'medium';
    const HIGH = 'high';
    const URGENT = 'urgent';

    public static function all(): array
    {
        return [
            self::LOW,
            self::MEDIUM,
            self::HIGH,
            self::URGENT,
        ];
    }

    public static function getDisplayName(string $priority): string
    {
        return match($priority) {
            self::LOW => 'Low',
            self::MEDIUM => 'Medium',
            self::HIGH => 'High',
            self::URGENT => 'Urgent',
            default => ucfirst($priority),
        };
    }

    public static function getColor(string $priority): string
    {
        return match($priority) {
            self::LOW => 'success',
            self::MEDIUM => 'info',
            self::HIGH => 'warning',
            self::URGENT => 'danger',
            default => 'secondary',
        };
    }
}
