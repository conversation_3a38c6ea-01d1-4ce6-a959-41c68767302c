<?php

namespace App\Observers;

use App\Models\User;
use App\Services\NotificationService;
use App\Enums\NotificationType;
use App\Enums\NotificationPriority;

class UserObserver
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Handle the User "created" event.
     */
    public function created(User $user)
    {
        // Send welcome notification to new user
        $this->notificationService->sendNotification(
            user: $user,
            title: "Welcome to Buzfi!",
            message: "Your account has been created successfully. Start shopping with amazing deals and offers!",
            type: NotificationType::ACCOUNT,
            priority: NotificationPriority::MEDIUM,
            link: "/",
            linkText: 'Start Shopping'
        );

        // Send notification to admin about new user registration
        if ($user->user_type !== 'admin') {
            $this->notificationService->sendAdminNotification(
                title: "New User Registration",
                message: "A new {$user->user_type} '{$user->name}' has registered on the platform",
                type: NotificationType::ACCOUNT,
                priority: NotificationPriority::LOW,
                link: $user->user_type === 'customer' ? route('customers.show', $user->id) : route('sellers.show', $user->id),
                linkText: 'View User',
                subject: $user,
                userId: $user->id
            );
        }
    }

    /**
     * Handle the User "updated" event.
     */
    public function updated(User $user)
    {
        $changes = $user->getDirty();

        // Check if user was verified
        if (isset($changes['email_verified_at']) && $user->email_verified_at) {
            $this->notificationService->sendNotification(
                user: $user,
                title: "Email Verified Successfully",
                message: "Your email has been verified successfully. You can now access all features.",
                type: NotificationType::ACCOUNT,
                priority: NotificationPriority::MEDIUM,
                link: "/dashboard",
                linkText: 'Go to Dashboard'
            );

            // Notify admin about email verification
            $this->notificationService->sendAdminNotification(
                title: "User Email Verified",
                message: "User '{$user->name}' has verified their email address",
                type: NotificationType::ACCOUNT,
                priority: NotificationPriority::LOW,
                link: $user->user_type === 'customer' ? route('customers.show', $user->id) : route('sellers.show', $user->id),
                linkText: 'View User',
                subject: $user,
                userId: $user->id
            );
        }

        // Check if user was banned/unbanned
        if (isset($changes['banned']) && $user->getOriginal('banned') !== $user->banned) {
            if ($user->banned) {
                $this->notificationService->sendNotification(
                    user: $user,
                    title: "Account Suspended",
                    message: "Your account has been suspended. Please contact support for more information.",
                    type: NotificationType::ACCOUNT,
                    priority: NotificationPriority::HIGH,
                    link: "/support",
                    linkText: 'Contact Support'
                );
            } else {
                $this->notificationService->sendNotification(
                    user: $user,
                    title: "Account Restored",
                    message: "Your account has been restored and you can now access all features.",
                    type: NotificationType::ACCOUNT,
                    priority: NotificationPriority::HIGH,
                    link: "/dashboard",
                    linkText: 'Go to Dashboard'
                );
            }
        }
    }
} 