<?php

namespace App\Http\Controllers\Api\V3\Products;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\ProductReviewRating\ProductReviewResource;
use App\Models\Product;
use App\Models\Review;
use App\Models\User;
use App\Services\ProductReviewRatingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use App\Helpers\NotificationHelper;

class ApiRatingReviewController extends ApiResponse
{
    protected ProductReviewRatingService $productReviewRatingService;
    
    public function __construct(ProductReviewRatingService $productReviewRatingService)
    {
        parent::__construct();
        $this->productReviewRatingService = $productReviewRatingService;
    }
    
    public function write_review_rating(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'reviewTitle' => 'required|string',
            'review' => 'required|string',
            'rating' => 'required|numeric',
            'product_slug' => 'required|exists:products,slug',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        
        $user = auth()->user();
        $product = Product::where('slug', $request->product_slug)->first();

        $upload = new \App\Utility\ApiAizUploadUtility();
        $result = $upload->multipleFileUpload($request->file('aiz_files'), $user->id);
        $resultVideo = $upload->multipleFileUpload($request->file('aiz_video_files'), $user->id);
        
        $review = new Review();
        $review->product_id = $product->id;
        $review->user_id = $user->id;
        $review->review_title = $request->reviewTitle;
        $review->photos = $result ? $result['files'] : '';
        $review->review_videos = $resultVideo ? $resultVideo['files'] : '';
        $review->rating = $request->rating;
        $review->comment = $request->review;
        $review->status = 0;
        $review->save();
        
        // Send notification to admin about new review
        NotificationHelper::reviewCreated($review);
        
        $per_page = 3;
        $page = 1;
        return $this->productReviewRatingService->get_product_reviews($per_page, $page, $status = 1, '', $user->id);
    }
    
    /*
    public function update_review_rating(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'reviewTitle' => 'required|string',
            'review' => 'required|string',
            'rating' => 'required|numeric',
            'review_id' => 'required|exists:reviews,id'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        
        $user = auth()->user();
        $review = Review::where('id', $request->review_id)->first();

        if (!$review || $review->user_id !== $user->id || $review->status !== 0) {
            return $this->error(
                'not_authorized',
                'You are not authorized to update this review',
                '',
                403
            );
        }

        $upload = new \App\Utility\ApiAizUploadUtility();
        $result = $upload->multipleFileUpload($request->file('aiz_files'), $user->id);
        $resultVideo = $upload->multipleFileUpload($request->file('aiz_video_files'), $user->id);

        $review->review_title = $request->reviewTitle;
        $review->photos = $result ? $result['files'] : '';
        $review->review_videos = $resultVideo ? $resultVideo['files'] : '';
        $review->rating = $request->rating;
        $review->comment = $request->review;
        $review->status = 0;
        $review->save();

        $per_page = 3;
        $page = 1;
        return $this->productReviewRatingService->get_product_reviews($per_page, $page, $status = 1, '', $user->id);
    }
    */

    public function delete_review($id): JsonResponse
    {
        $validator = Validator::make(['review_id' => $id], [
            'review_id' => 'required|exists:reviews,id'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid review ID',
                $validator->errors()->messages(),
                400
            );
        }

        $user = auth()->user();
        $review = Review::where('id', $id)->first();

        if (!$review || $review->user_id !== $user->id || $review->status !== 0) {
            return $this->error_response('You are not authorized to delete this review', [], 403);
        }

        try {
            $review->delete();
            return $this->success_response('Review deleted successfully');
        } catch (\Exception $e) {
            return $this->error_response('Failed to delete review', [], 500);
        }
    }

    public function get_user_reviews(Request $request): JsonResponse
    {
        $user = auth()->user();
        
        // Handle frontend parameters
        $order_by = $request->input('order_by', 'newest');
        $search = $request->input('search', '');
        $min_rating = $request->input('min_rating');
        $max_rating = $request->input('max_rating');
        $per_page = min((int)$request->input('per_page', 10), 50);
        $page = max((int)$request->input('page', 1), 1);
        
        // Convert order_by to sort for the service
        $sort_mapping = [
            'newest' => 'newest',
            'oldest' => 'oldest', 
            'highest' => 'highest_rating',
            'lowest' => 'lowest_rating',
            'most_helpful' => 'most_helpful'
        ];
        $sort = $sort_mapping[$order_by] ?? 'newest';
        
        // Handle rating filter
        $filter_by_rating = '';
        if ($min_rating && $max_rating && $min_rating == $max_rating) {
            $filter_by_rating = in_array($min_rating, ['1', '2', '3', '4', '5']) ? $min_rating : '';
        }
        
        return $this->productReviewRatingService->get_product_reviews($per_page, $page, '', '', $user->id, $filter_by_rating, $sort, $search);
    }
    
    public function get_user_review_details(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'review_id' => 'required|exists:reviews,id'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        
        $user = auth()->user();
        $review = Review::where('id', $request->review_id)->first();
        
        if (!$review || $review->user_id !== $user->id) {
            return $this->error(
                'not_authorized',
                'You are not authorized to view this review',
                '',
                403
            );
        }
        
        return $this->success(new ProductReviewResource($review));
    }
    
    public function get_user_rating(Request $request)
    {
        $user = auth()->user();

        $average = Review::where('user_id', $user->id)
            ->where('status', 1)
            ->avg('rating');

        $ratings = Review::where('user_id', $user->id)
            ->where('status', 1)
            ->selectRaw('rating, COUNT(*) as total')
            ->groupBy('rating')
            ->pluck('total', 'rating');

        return $this->success([
            'rating' => number_format($average, 1),
            'distribution' => $ratings
        ]);
    }

    public function get_seller_reviews(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'seller_id' => 'required|exists:users,id',
            'per_page' => 'required|integer|min:1',
            'page' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        $seller = User::find($request->seller_id);
        if (!$seller) {
            return $this->error(
                'seller_not_found',
                'Seller not found',
                '',
                404
            );
        }

        $per_page = $request->input('per_page', 3);
        $page = $request->input('page', 1);

        $reviews = Review::where('seller_id', $seller->id)
            ->where('status', 1)
            ->orderBy('created_at', 'desc')
            ->paginate($per_page, ['*'], 'page', $page);

        $data['totalReviews'] = $reviews->total();
        $data['reviews'] = $reviews->items();
        $data['averageRating'] = $reviews->avg('rating');
        $data['ratingPercentageBreakdown'] = $reviews->groupBy('rating')->map(function ($group) {
            return [
                'rating' => $group->first()->rating,
                'percentage' => round($group->count() / $reviews->count() * 100, 2)
            ];
        });
        
        $data = new ProductReviewResource($data);
        return $this->success($data);
    }

    public function get_seller_rating_stats(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'seller_id' => 'required|exists:users,id',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        $seller = User::find($request->seller_id);
        if (!$seller) {
            return $this->error(
                'seller_not_found',
                'Seller not found',
                '',
                404
            );
        }

        $averageRating = Review::where('seller_id', $seller->id)
            ->where('status', 1)
            ->avg('rating');

        $ratings = Review::where('seller_id', $seller->id)
            ->where('status', 1)
            ->selectRaw('rating, COUNT(*) as total')
            ->groupBy('rating') 
            ->pluck('total', 'rating');

        $totalReviews = $ratings->sum();

        $percentageBreakdown = collect(range(1, 5))->mapWithKeys(function ($star) use ($ratings, $totalReviews) {
            $count = $ratings->get($star, 0);
            $percentage = $totalReviews > 0 ? round(($count / $totalReviews) * 100, 2) : 0;
            return [$star => $percentage];
        });

        $data['averageRating'] = round($averageRating ?? 0, 2);
        $data['ratingPercentageBreakdown'] = $percentageBreakdown;  
        return $this->success($data);
    }

    public function respond_to_review(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'review_id' => 'required|exists:reviews,id',
            'response' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        $user = auth()->user(); 
        $review = Review::where('id', $request->review_id)->first();

        if (!$review || $review->seller_id !== $user->id) {
            return $this->error(
                'not_authorized',
                'You are not authorized to respond to this review',         
                '',
                403
            );
        }

        $review->seller_response = $request->response;
        $review->save();        
        return $this->success_response('Review response saved successfully');
    }   

    public function update_review_response(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'review_id' => 'required|exists:reviews,id',
            'response' => 'required|string',    
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information', 
                $validator->errors()->messages(),
                400
            );
        }

        $user = auth()->user();
        $review = Review::where('id', $request->review_id)->first();

        if (!$review || $review->seller_id !== $user->id) {
            return $this->error(
                'not_authorized',
                'You are not authorized to update this review response',    
                '',
                403
            );
        }

        $review->seller_response = $request->response;  
        $review->save();
        return $this->success_response('Review response updated successfully');
    }

    public function delete_review_response(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [     
            'review_id' => 'required|exists:reviews,id',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        $user = auth()->user();
        $review = Review::where('id', $request->review_id)->first();

        if (!$review || $review->seller_id !== $user->id) {
            return $this->error(
                'not_authorized',
                'You are not authorized to delete this review response',
                '',
                403
            );
        }

        $review->seller_response = null;
        $review->save();
        return $this->success_response('Review response deleted successfully');
    }

    public function get_seller_reviews_stats(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'seller_id' => 'required|exists:users,id',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400 
            );
        }

        $seller = User::find($request->seller_id);
        if (!$seller) {
            return $this->error(
                'seller_not_found',
                'Seller not found',
                '',
                404
            );
        }   

        $totalReviews = Review::where('seller_id', $seller->id)
            ->where('status', 1)
            ->count();

        $ratingBreakdown = Review::where('seller_id', $seller->id)  
            ->where('status', 1)
            ->selectRaw('rating, COUNT(*) as total')
            ->groupBy('rating')
            ->pluck('total', 'rating');

        $data['totalReviews'] = $totalReviews;
        $data['ratingBreakdown'] = $ratingBreakdown;

        return $this->success($data);
    }

    /**
     * Mark a review as helpful
     * 
     * @param string $reviewId
     * @return JsonResponse
     */
    public function mark_review_helpful(string $reviewId): JsonResponse
    {
        try {
            $review = Review::findOrFail($reviewId);
            
            // Increment helpful count
            $review->increment('helpful_count');
            
            return $this->success_response('Review marked as helpful successfully');
        } catch (\Exception $e) {
            return $this->error_response('Failed to mark review as helpful', [], 500);
        }
    }

    public function get_user_reviews_v2(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();
            if (!$user) {
                return $this->error_response('Authentication failed. Please log in.', 401);
            }

            // Get parameters
            $perPage = $request->get('per_page', 10);
            $page = $request->get('page', 1);
            $orderBy = $request->get('order_by', 'newest');
            $search = $request->get('search');
            $minRating = $request->get('min_rating');
            $maxRating = $request->get('max_rating');
            $verifiedOnly = $request->get('verified_only');
            $withMedia = $request->get('with_media');
            $withResponse = $request->get('with_response');

            // Use the existing get_product_reviews method with user_id parameter
            $serviceResponse = $this->productReviewRatingService->get_product_reviews(
                $perPage, 
                $page, 
                $user->id, 
                $search, 
                $minRating, 
                $maxRating, 
                $verifiedOnly, 
                $withMedia, 
                $withResponse, 
                $orderBy,
                $status='1'
            );

            // The service returns a JsonResponse, so we need to extract the data
            $responseData = json_decode($serviceResponse->getContent(), true);
            
            if ($responseData['status'] === 'success') {
                return response()->json([
                    'status' => 'success',
                    'data' => $responseData['data'],
                    'message' => 'User reviews retrieved successfully'
                ]);
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to retrieve user reviews',
                    'error' => $responseData['message'] ?? 'Unknown error'
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Error retrieving user reviews: ' . $e->getMessage()
            ], 500);
        }
    }
}   