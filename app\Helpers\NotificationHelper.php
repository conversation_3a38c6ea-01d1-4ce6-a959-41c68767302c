<?php

namespace App\Helpers;

use App\Services\NotificationService;
use App\Enums\NotificationType;
use App\Enums\NotificationPriority;
use App\Models\User;
use App\Models\Order;
use App\Models\Product;
use App\Models\SupportTicket;
use Illuminate\Support\Facades\Log;

class NotificationHelper
{
    protected static $notificationService;

    public static function getService(): NotificationService
    {
        if (!self::$notificationService) {
            self::$notificationService = app(NotificationService::class);
        }
        return self::$notificationService;
    }

    /**
     * Send order notification to admin
     */
    public static function orderCreated(Order $order): void
    {
        self::getService()->sendAdminNotification(
            title: "New Order - #{$order->id}",
            message: "Order placed by {$order->user->name} for " . single_price($order->grand_total),
            type: NotificationType::ORDER,
            priority: NotificationPriority::HIGH,
            link: route('all_orders.show', $order->id),
            linkText: 'View Order',
            subject: $order,
            userId: $order->user_id
        );
    }

    /**
     * Send order status update notification
     */
    public static function orderStatusUpdated(Order $order, string $oldStatus, string $newStatus): void
    {
        // Notify customer
        self::getService()->sendNotification(
            user: $order->user,
            title: "Order Status Updated - #{$order->id}",
            message: "Your order status has been updated from {$oldStatus} to {$newStatus}",
            type: NotificationType::ORDER,
            priority: NotificationPriority::HIGH,
            link: "/orders/{$order->id}",
            linkText: 'View Order',
            subject: $order
        );

        // Notify admin
        self::getService()->sendAdminNotification(
            title: "Order Status Changed - #{$order->id}",
            message: "Order #{$order->id} status changed from {$oldStatus} to {$newStatus}",
            type: NotificationType::ORDER,
            priority: NotificationPriority::MEDIUM,
            link: route('all_orders.show', $order->id),
            linkText: 'View Order',
            subject: $order,
            userId: $order->user_id
        );
    }

    /**
     * Send payment notification
     */
    public static function paymentReceived(Order $order, float $amount): void
    {
        // Notify customer
        self::getService()->sendNotification(
            user: $order->user,
            title: "Payment Confirmed - #{$order->code}",
            message: "Your payment of " . single_price($amount) . " has been confirmed",
            type: NotificationType::PAYMENT,
            priority: NotificationPriority::HIGH,
            link: "/" . ($order->user->user_type === 'b2b' ? 'dropshipper' : $order->user->user_type) . "/orders/{$order->code}",
            linkText: 'View Order',
            subject: $order
        );

        // Notify admin
        self::getService()->sendAdminNotification(
            title: "Payment Received - #{$order->code}",
            message: "Payment of " . single_price($amount) . " received for order #{$order->id}",
            type: NotificationType::PAYMENT,
            priority: NotificationPriority::HIGH,
            link: route('all_orders.show', $order->id),
            linkText: 'View Order',
            subject: $order,
            userId: $order->user_id
        );
    }

    /**
     * Send support ticket notification
     */
    public static function supportTicketCreated(SupportTicket $ticket): void
    {
        // Notify customer
        self::getService()->sendNotification(
            user: $ticket->user,
            title: "Support Ticket Created - #{$ticket->id}",
            message: "Your support ticket has been created. We'll respond soon.",
            type: NotificationType::SUPPORT,
            priority: NotificationPriority::MEDIUM,
            link: "/support-tickets/{$ticket->id}",
            linkText: 'View Ticket',
            subject: $ticket
        );

        // Notify admin
        self::getService()->sendAdminNotification(
            title: "New Support Ticket - #{$ticket->id}",
            message: "New support ticket '{$ticket->subject}' from {$ticket->user->name}",
            type: NotificationType::SUPPORT,
            priority: self::getTicketPriority($ticket->priority ?? 'medium'),
            link: route('support_ticket.admin_show', $ticket->id),
            linkText: 'View Ticket',
            subject: $ticket,
            userId: $ticket->user_id
        );
    }

    /**
     * Send return request notification
     */
    public static function returnRequestCreated($returnRequest): void
    {
        // Notify customer
        // self::getService()->sendNotification(
        //     user: $returnRequest->user,
        //     title: "Return Request Submitted - #{$returnRequest->id}",
        //     message: "Your return request has been submitted for review",
        //     type: NotificationType::ORDER,
        //     priority: NotificationPriority::MEDIUM,
        //     link: "/return-requests/{$returnRequest->id}",
        //     linkText: 'View Request',
        //     subject: $returnRequest
        // );

        // Notify admin
        self::getService()->sendAdminNotification(
            title: "New Return Request - #{$returnRequest->id}",
            message: "Return request submitted by {$returnRequest->user->name} for order #{$returnRequest->order_id}",
            type: NotificationType::ORDER,
            priority: NotificationPriority::HIGH,
            link: route('all_orders.show', $returnRequest->order_id),
            linkText: 'View Request',
            subject: $returnRequest,
            userId: $returnRequest->user_id
        );
    }

    /**
     * Send stock alert notification
     */
    public static function stockAlert(Product $product, string $alertType = 'low_stock'): void
    {
        $title = match($alertType) {
            'out_of_stock' => 'Out of Stock Alert',
            'low_stock' => 'Low Stock Alert',
            default => 'Stock Alert'
        };

        $message = match($alertType) {
            'out_of_stock' => "Product '{$product->name}' is out of stock!",
            'low_stock' => "Product '{$product->name}' is low on stock. Current: {$product->current_stock}",
            default => "Stock alert for '{$product->name}'"
        };

        $priority = $alertType === 'out_of_stock' ? NotificationPriority::URGENT : NotificationPriority::HIGH;

        self::getService()->sendAdminNotification(
            title: $title,
            message: $message,
            type: NotificationType::STOCK,
            priority: $priority,
            link: route('products.admin.edit', $product->id),
            linkText: 'Update Stock',
            subject: $product,
            userId: $product->user_id
        );
    }

    /**
     * Send user registration notification
     */
    public static function userRegistered(User $user): void
    {
        // Welcome notification to user
        self::getService()->sendNotification(
            user: $user,
            title: "Welcome to Buzfi!",
            message: "Your account has been created successfully. Start exploring our amazing products!",
            type: NotificationType::ACCOUNT,
            priority: NotificationPriority::MEDIUM,
            link: "/",
            linkText: 'Start Shopping'
        );

        // Notify admin
        if ($user->user_type !== 'admin') {
            self::getService()->sendAdminNotification(
                title: "New User Registration",
                message: "New {$user->user_type} '{$user->name}' has registered",
                type: NotificationType::ACCOUNT,
                priority: NotificationPriority::LOW,
                link: $user->user_type === 'customer' ? route('customers.show', $user->id) : route('sellers.show', $user->id),
                linkText: 'View User',
                subject: $user,
                userId: $user->id
            );
        }
    }

    /**
     * Send custom admin notification
     */
    public static function customAdminNotification(string $title, string $message, string $type = NotificationType::SYSTEM, string $priority = NotificationPriority::MEDIUM, ?string $link = null, ?string $linkText = null): void
    {
        self::getService()->sendAdminNotification(
            title: $title,
            message: $message,
            type: $type,
            priority: $priority,
            link: $link,
            linkText: $linkText
        );
    }

    /**
     * Send ticket created notification to both user and admin
     */
    public static function sendTicketCreatedNotification(SupportTicket $ticket): void
    {
        // Send confirmation to user
        self::getService()->sendNotification(
            user: $ticket->user,
            title: "Support Ticket Created",
            message: "Your support ticket #{$ticket->id} '{$ticket->subject}' has been created. We'll respond within 24 hours.",
            type: NotificationType::SUPPORT,
            priority: self::getTicketPriority($ticket->priority),
            link: route('support_tickets.index'),
            linkText: 'View Ticket'
        );

        // Notify admin about new ticket
        self::getService()->sendAdminNotification(
            title: "New Support Ticket #{$ticket->id}",
            message: "New {$ticket->priority} priority ticket created by {$ticket->user->name}: '{$ticket->subject}'",
            type: NotificationType::SUPPORT,
            priority: self::getTicketPriority($ticket->priority),
            link: route('support_ticket.index'),
            linkText: 'View Ticket',
            subject: $ticket,
            userId: $ticket->user_id
        );
    }

    /**
     * Send review created notification to admin and seller
     */
    public static function reviewCreated($review): void
    {
        $product = $review->product;
        $user = $review->user;
        
        // Send notification to admin
        self::getService()->sendAdminNotification(
            title: "New Product Review",
            message: "New {$review->rating}-star review for '{$product->name}' by {$user->name}",
            type: NotificationType::SYSTEM,
            priority: NotificationPriority::LOW,
            link: route('reviews.index'),
            linkText: 'View Review',
            subject: $review,
            userId: $review->user_id
        );

        // Send notification to seller if not admin
        if ($product->user && $product->user->user_type !== 'admin') {
            self::getService()->sendNotification(
                user: $product->user,
                title: "New Review for Your Product",
                message: "'{$product->name}' received a {$review->rating}-star review from {$user->name}",
                type: NotificationType::PRODUCT,
                priority: NotificationPriority::MEDIUM,
                link: route('products.admin.edit', $product->id),
                linkText: 'View Product'
            );
        }
    }

    /**
     * Send return request notification to admin and user
     */
    public static function sendReturnRequestNotification($returnRequest): void
    {
        $user = $returnRequest->user;
        $order = $returnRequest->order;
        
        // Send confirmation to user
        self::getService()->sendNotification(
            user: $user,
            title: "Return Request Submitted",
            message: "Your return request #{$returnRequest->return_code} for order #{$order->code} has been submitted. We'll review it within 24 hours.",
            type: NotificationType::ORDER,
            priority: NotificationPriority::MEDIUM,
            link: route('purchase_history.index'),
            linkText: 'View Order'
        );

        // Notify admin
        self::getService()->sendAdminNotification(
            title: "New Return Request - #{$returnRequest->return_code}",
            message: "Return request submitted by {$user->name} for order #{$order->code}. Reason: {$returnRequest->reason_for_return}",
            type: NotificationType::ORDER,
            priority: NotificationPriority::HIGH,
            link: route('return_request_details', $returnRequest->return_code),
            linkText: 'View Request',
            subject: $returnRequest,
            userId: $returnRequest->user_id
        );
    }

    /**
     * Get ticket priority
     */
    private static function getTicketPriority(string $ticketPriority): string
    {
        return match(strtolower($ticketPriority)) {
            'high', 'urgent' => NotificationPriority::HIGH,
            'low' => NotificationPriority::LOW,
            default => NotificationPriority::MEDIUM,
        };
    }

    /**
     * Send instant admin notification with source specification
     */
    public static function sendInstantAdminNotification(
        string $title,
        string $message,
        string $type = 'SYSTEM',
        string $priority = 'MEDIUM',
        ?string $link = null,
        ?string $linkText = null,
        ?array $data = null,
        ?int $relatedUserId = null,
        string $source = 'frontend' // frontend or admin
    ) {
        try {
            Log::info('Starting sendInstantAdminNotification', [
                'title' => $title,
                'type' => $type,
                'priority' => $priority,
                'source' => $source
            ]);
            
            // Get admin user ID (default admin or current admin)
            $adminUserId = 9; // Default admin user ID
            if(auth()->check() && auth()->user()->user_type === 'admin') {
                $adminUserId = auth()->user()->id;
            }
            
            // Create notification using UserNotification model directly
            $notification = \App\Models\UserNotification::create([
                'user_id' => $adminUserId,
                'type' => strtolower($type),
                'subject_id' => $relatedUserId ?? 1,
                'subject_type' => 'App\\Models\\User',
                'title' => $title,
                'message' => $message,
                'priority' => strtolower($priority),
                'read' => false,
                'link' => $link,
                'link_text' => $linkText,
                'source' => $source, // Add source field
                'data' => $data ? json_encode($data) : null,
            ]);

            Log::info('Instant admin notification sent', [
                'notification_id' => $notification->id,
                'title' => $title,
                'type' => $type,
                'source' => $source
            ]);

            return $notification;

        } catch (\Exception $e) {
            Log::error('Failed to send instant admin notification: ' . $e->getMessage(), [
                'title' => $title,
                'type' => $type,
                'source' => $source,
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // Don't throw exception to avoid breaking main process
            return null;
        }
    }

    /**
     * Send notification to user (triggered by admin actions)
     */
    public static function sendUserNotification(
        int $userId,
        string $title,
        string $message,
        string $type = 'order',
        string $priority = 'medium',
        ?string $link = null,
        ?string $linkText = null,
        ?array $data = null,
        string $source = 'admin' // frontend or admin
    ) {
        try {
            Log::info('Starting sendUserNotification', [
                'user_id' => $userId,
                'title' => $title,
                'type' => $type,
                'priority' => $priority,
                'source' => $source
            ]);
            
            // Create notification for specific user
            $notification = \App\Models\UserNotification::create([
                'user_id' => $userId,
                'type' => strtolower($type),
                'subject_id' => $userId,
                'subject_type' => 'App\\Models\\User',
                'title' => $title,
                'message' => $message,
                'priority' => strtolower($priority),
                'read' => false,
                'link' => $link,
                'link_text' => $linkText,
                'source' => $source, // Add source field
                'data' => $data ? json_encode($data) : null,
            ]);

            Log::info('User notification sent', [
                'notification_id' => $notification->id,
                'user_id' => $userId,
                'title' => $title,
                'type' => $type,
                'source' => $source
            ]);

            return $notification;

        } catch (\Exception $e) {
            Log::error('Failed to send user notification: ' . $e->getMessage(), [
                'user_id' => $userId,
                'title' => $title,
                'type' => $type,
                'source' => $source,
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // Don't throw exception to avoid breaking main process
            return null;
        }
    }

    /**
     * Trigger notification from frontend action
     */
    public static function triggerFromFrontend(string $action, array $data): void
    {
        try {
            switch ($action) {
                case 'order_placed':
                    if (isset($data['order_id'])) {
                        $order = \App\Models\Order::find($data['order_id']);
                        if ($order) {
                            self::orderCreated($order);
                        }
                    }
                    break;

                case 'payment_received':
                    if (isset($data['order_id'], $data['amount'])) {
                        $order = \App\Models\Order::find($data['order_id']);
                        if ($order) {
                            self::paymentReceived($order, $data['amount']);
                        }
                    }
                    break;

                case 'support_ticket_created':
                    if (isset($data['ticket_id'])) {
                        $ticket = \App\Models\SupportTicket::find($data['ticket_id']);
                        if ($ticket) {
                            self::supportTicketCreated($ticket);
                        }
                    }
                    break;

                case 'return_request_created':
                    if (isset($data['return_request_id'])) {
                        $returnRequest = \App\Models\ReturnRequestInfo::find($data['return_request_id']);
                        if ($returnRequest) {
                            self::returnRequestCreated($returnRequest);
                        }
                    }
                    break;

                case 'product_review_created':
                    if (isset($data['review_id'])) {
                        $review = \App\Models\Review::find($data['review_id']);
                        if ($review) {
                            self::reviewCreated($review);
                        }
                    }
                    break;

                case 'user_registered':
                    if (isset($data['user_id'])) {
                        $user = \App\Models\User::find($data['user_id']);
                        if ($user) {
                            self::userRegistered($user);
                        }
                    }
                    break;

                case 'stock_alert':
                    if (isset($data['product_id'], $data['alert_type'])) {
                        $product = \App\Models\Product::find($data['product_id']);
                        if ($product) {
                            self::stockAlert($product, $data['alert_type']);
                        }
                    }
                    break;

                case 'custom_admin':
                    if (isset($data['title'], $data['message'])) {
                        self::customAdminNotification(
                            $data['title'],
                            $data['message'],
                            $data['type'] ?? NotificationType::SYSTEM,
                            $data['priority'] ?? NotificationPriority::MEDIUM,
                            $data['link'] ?? null,
                            $data['linkText'] ?? null
                        );
                    }
                    break;

                default:
                    \Log::warning("Unknown notification action: {$action}");
                    break;
            }
        } catch (\Exception $e) {
            \Log::error("Failed to trigger notification for action {$action}: " . $e->getMessage());
        }
    }

    /**
     * Batch notify admins for multiple events
     */
    public static function batchNotifyAdmins(array $notifications): void
    {
        try {
            foreach ($notifications as $notification) {
                if (isset($notification['title'], $notification['message'])) {
                    self::sendInstantAdminNotification(
                        title: $notification['title'],
                        message: $notification['message'],
                        type: $notification['type'] ?? NotificationType::SYSTEM,
                        priority: $notification['priority'] ?? NotificationPriority::MEDIUM,
                        link: $notification['link'] ?? null,
                        linkText: $notification['linkText'] ?? null,
                        subject: $notification['subject'] ?? null,
                        userId: $notification['userId'] ?? null
                    );
                }
            }
        } catch (\Exception $e) {
            \Log::error('Failed to send batch admin notifications: ' . $e->getMessage());
        }
    }

    /**
     * Get unread notifications count for frontend
     */
    public static function getUnreadCountForUser(int $userId): int
    {
        try {
            return \App\Models\Notification::where('user_id', $userId)
                ->whereNull('read_at')
                ->count();
        } catch (\Exception $e) {
            \Log::error('Failed to get unread count for user: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get unread notifications count for admin
     */
    public static function getUnreadCountForAdmin(): int
    {
        try {
            return \App\Models\Notification::where('user_type', 'admin')
                ->whereNull('read_at')
                ->count();
        } catch (\Exception $e) {
            \Log::error('Failed to get unread count for admin: ' . $e->getMessage());
            return 0;
        }
    }
} 