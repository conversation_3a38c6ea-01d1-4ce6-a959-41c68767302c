<?php

namespace App\Http\Controllers\Api\V3\Auth;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\User\DropshipperProfileResource;
use App\Http\Resources\V3\User\SellerProfileResource;
use App\Http\Resources\V3\User\UserResource;
use App\Models\B2BProfile;
use App\Models\User;
use App\Notifications\OTPEmailVerificationNotification;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ApiRegisterController extends ApiResponse
{
    public function register(Request $request)
    {
        // Validate request data
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'email' => 'required|email|unique:users',
            'password' => 'required|min:6',
            'confirmPassword' => 'required|same:password',
            'user_type' => 'required|in:customer,seller,dropshipper'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        // $phone =$request->phone;
        // $phoneValid = phoneNumberFormatValidation($phone);

        // if (!$phoneValid)
        // {
        //     return $this->error(400,
        //         'Invalid phone number.',
        //         $phone. ", is not a valid mobile number. Please correct and try again."
        //     );
        // }
        try {
            $user = new User();
            $user->name = $request->name;
            $user->email = $request->email;
            $user->password = bcrypt($request->password);
            $user->user_type = 'customer';
            $user->email_verification_token =  Str::uuid();
            $user->save();
            $user->notify(new OTPEmailVerificationNotification());
            $data = $this->generateAuthData($user);
            return $this->success(
                $data,
                'Registration successful. Please verify your email.',
                201
            );
        } catch (Exception $exception) {
            Log::channel('api_registration')->error('Error in user registration in : ApiRegisterController::register ' . print_r($exception->getMessage(),true));
            $user->delete();
            return $this->error(400,
                'Registration failed.',
                'Registration failed.Please try again later.'
            );
        }
    }
    public function register_seller(Request $request)
    {

        // Validate request data
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'email' => 'required|email|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'password_confirmation' => 'required|string|same:password',
            'phone' => 'required|regex:/^\+?\d{10,15}$/',
            'business_name' => 'required|string',
            'business_address' => 'required|array',
            'business_address.street' => 'required|string',
            'business_address.city' => 'required|string',
            'business_address.state' => 'required|string',
            'business_address.zipCode' => 'required|string',
            'business_address.country' => 'required|string',
            'tax_id' => 'required|string',
            'business_type' => 'required|string',
            'website' => 'nullable|url',
            'category' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        $phone =$request->phone;
        $phoneValid = phoneNumberFormatValidation($phone);
        if (!$phoneValid)
        {
            return $this->error(400,
                'Invalid phone number.',
                $phone. ", is not a valid mobile number. Please correct and try again."
            );
        }

        DB::beginTransaction();
        try {
            $user = new User();
            $user->name = $request->name;
            $user->email = $request->email;
            $user->password = bcrypt($request->password);
            $user->user_type = 'seller';
            $user->phone = $request->phone;
            $user->email_verification_token =  Str::uuid();
            $user->save();


            $seller = new B2BProfile();
            $seller->slug = preg_replace('/[^A-Za-z0-9\-]/', '', str_replace(' ', '-', $request->name)).'-'.Str::random(5);;
            $seller->user_id = $user->id;
            $seller->verification_status = 0;
            $seller->name = $request->business_name;
            $seller->business_address = json_encode($request->business_address);
            $seller->tax_id = $request->tax_id;
            $seller->business_type = $request->business_type;
            $seller->website = $request->website;
            $seller->business_category = $request->category;
            $seller->save();

            $user->notify(new OTPEmailVerificationNotification());
            $data = $this->generateAuthData(
                $user,
                $seller,
                'seller');
            DB::commit();
            return $this->success(
                $data,
                'Registration successful. Please verify your email.',
                201
            );
        } catch (Exception $exception) {
            Log::channel('api_registration')->error('Error in user registration in : ApiRegisterController::register ' . print_r($exception->getMessage(),true));
            DB::rollBack();
            return $this->error(400,
                'Registration failed.',
                'Registration failed.Please try again later.'
            );
        }
    }
    public function register_dropshipper(Request $request)
    {

        // Validate request data
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'email' => 'required|email|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'password_confirmation' => 'required|string|same:password',
            //'phone' => 'required|regex:/^\+?\d{10,15}$/',
            'company_name' => 'required|string',

            // Business Address Validation
            'business_address' => 'required|array',
            'business_address.street' => 'required|string',
            'business_address.city' => 'required|string',
            'business_address.state' => 'required|string',
            'business_address.zipCode' => 'required|string',
            'business_address.country' => 'required|string',

            // Shipping Address Validation
            'shipping_address' => 'required|array',
            'shipping_address.street' => 'required|string',
            'shipping_address.city' => 'required|string',
            'shipping_address.state' => 'required|string',
            'shipping_address.zipCode' => 'required|string',
            'shipping_address.country' => 'required|string',

            'tax_id' => 'required|string',
            'business_type' => 'required|string',
            'website' => 'nullable|url',
        ]);
        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        $phone =$request->phone;
        /*$phoneValid = phoneNumberFormatValidation($phone);
        if (!$phoneValid)
        {
            return $this->error(400,
                'Invalid phone number.',
                $phone. ", is not a valid mobile number. Please correct and try again."
            );
        }*/

        DB::beginTransaction();
        try {
            $user = new User();
            $user->name = $request->name;
            $user->email = $request->email;
            $user->password = bcrypt($request->password);
            $user->user_type = 'dropshipper';
            $user->phone = $request->phone;
            $user->email_verification_token =  Str::uuid();
            $user->save();


            $dropshipper = new B2BProfile();
            $dropshipper->slug = preg_replace('/[^A-Za-z0-9\-]/', '', str_replace(' ', '-', $request->name)).'-'.Str::random(5);;
            $dropshipper->user_id = $user->id;
            $dropshipper->verification_status = 0;
            $dropshipper->name = $request->company_name;
            $dropshipper->business_address = json_encode($request->business_address);
            $dropshipper->shipping_address = json_encode($request->shipping_address);
            $dropshipper->tax_id = $request->tax_id;
            $dropshipper->business_type = $request->business_type;
            $dropshipper->website = $request->website;
            $dropshipper->save();


            $user->notify(new OTPEmailVerificationNotification());
            $data = $this->generateAuthData(
                $user,
                $dropshipper,
                'dropshipper');

            DB::commit();
            return $this->success(
                $data,
                'Registration successful. Please verify your email.',
                201
            );
        } catch (Exception $exception) {
            Log::channel('api_registration')->error('Error in user registration in : ApiRegisterController::register ' . print_r($exception->getMessage(),true));
            DB::rollBack();
            return $this->error(400,
                'Registration failed.',
                'Registration failed.Please try again later.'
            );
        }
    }
    protected function generateAuthData(User $user,$dropshipper_or_seller_data = null,$user_type = 'user')
    {
        $token = $user->createToken('API Token');
        if($user_type == 'seller'){
            return [
                'user' => new UserResource($user),
                'seller' => new SellerProfileResource($dropshipper_or_seller_data),
                'token' => [
                    'access_token' => $token->plainTextToken,
                    'token_type' => 'Bearer',
                    'expires_in' => config('sanctum.expiration') * 60
                ]
            ];
        }else if ($user_type == 'dropshipper') {

            return [
                'user' => new UserResource($user),
                'dropshipper' => new DropshipperProfileResource($dropshipper_or_seller_data),
                'token' => [
                    'access_token' => $token->plainTextToken,
                    'token_type' => 'Bearer',
                    'expires_in' => config('sanctum.expiration') * 60
                ]
            ];
        }else{
            return [
                'user' => new UserResource($user),
                'token' => [
                    'access_token' => $token->plainTextToken,
                    'token_type' => 'Bearer',
                    'expires_in' => config('sanctum.expiration') * 60
                ]
            ];
        }
    }
    public function resendCode(Request $request){
        $user = auth()->user();
        if($user->email_verified_at != null){
            return $this->error('Already Verified','Your account is already verified',null,200);
        }
        $user->email_verification_token =  Str::uuid();
        $user->save();
        $user->notify(new OTPEmailVerificationNotification());
        return $this->success(new UserResource($user),'Verification code is sent again');
    }
    public function confirmCode(Request $request)
    {
        $messages = array(
            'verification_code.required' => translate('Verification Code is required'),
            'verification_code.min' => translate('Minimum 6 digits required for Verification Code')
        );
        $validator = Validator::make($request->all(), [
            'verification_code' => 'required|min:6',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }

        $user = auth()->user();
        if ($user->verification_code != $request->verification_code) {
            return $this->error(400,'Verification Code does not match, you can request for resending the code.');
        }
        $userTokenExpiry = Carbon::parse($user->verification_token_expire_at);
        if ($userTokenExpiry->isPast()) {
            return $this->error(400,'Verification Code Expired.Please Click on Resend verification email !');
        }
        $user->verification_token_expire_at = Null;
        $user->verification_code = null;
        $user->email_verification_token = null;
        $user->email_verified_at = Carbon::now();
        $user->save();
        return $this->success(new UserResource($user),'Your account is now verified');
    }

    public function verifyOtp(Request $request)
    {
        // $user = auth()->user();
        // if($user->email_verified_at != null){
        //     return $this->error('Already Verified','Your account is already verified',null,200);
        // }
        // return $this->success($request->all(),'Verification code');
        $messages = array(
            'verification_code.required' => translate('Verification Code is required'),
            'verification_code.min' => translate('Minimum 6 digits required for Verification Code')
        );
        $validator = Validator::make($request->all(), [
            'verification_code' => 'required|min:6',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }
        $user = auth()->user();
        if ($user->verification_code != $request->verification_code) {
            return $this->error(400,'Verification Code does not match, you can request for resending the code.');
        }
        $user->verification_code = null;
        $user->email_verification_token = null;
        $user->email_verified_at = Carbon::now();
        $user->save();
        return $this->success(new UserResource($user),'Your account is now verified');
    }

    public function resendOtp(Request $request)
    {
        $messages = array(
            'email.required' => translate('Email is required'),
            'email.email' => translate('Email must be a valid email address')
        );
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }
        $user = User::where('email', $request->email)->first();
        if (!$user) {
            return $this->error(400,'User not found');
        }
        $user->notify(new OTPEmailVerificationNotification());
        return $this->success(null,'Verification code sent successfully');
    }
}
