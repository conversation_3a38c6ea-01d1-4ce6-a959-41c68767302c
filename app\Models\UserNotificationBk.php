<?php

namespace App\Models;

use App\Enums\NotificationPriority;
use App\Enums\NotificationType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Str;

class UserNotificationBk extends Model
{
    use HasFactory;
    protected $fillable = [
        'user_id',
        'notification_type',
        'type',
        'subject_id',
        'subject_type',
        'title',
        'message',
        'priority',
        'read',
        'link',
        'data',
        'read_at',
        'created_at',
        'updated_at'
    ];
    protected $casts = [
        'read' => 'boolean',
        'data' => 'array',
        'read_at' => 'datetime',
        'notification_type' => NotificationType::class,
        'priority' => NotificationPriority::class,
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        static::creating(function ($notification) {
            $notification->id = (string) Str::uuid();
        });
    }

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Get the user that owns the notification.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the subject model of the notification.
     */
    public function subject(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the link text or a default value.
     */
    public function getLinkTextAttribute($value)
    {
        return $value ?: 'View Details';
    }

    /**
     * Scope a query to only include unread notifications.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUnread($query)
    {
        return $query->where('read', false)->whereNull('read_at');
    }

    /**
     * Scope a query to only include notifications of a given type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Mark the notification as read.
     *
     * @return bool
     */
    public function markAsRead()
    {
        return $this->update([
            'read' => true,
            'read_at' => now()
        ]);
    }

    /**
     * Mark the notification as unread.
     *
     * @return bool
     */
    public function markAsUnread()
    {
        return $this->update([
            'read' => false,
            'read_at' => null
        ]);
    }
}
