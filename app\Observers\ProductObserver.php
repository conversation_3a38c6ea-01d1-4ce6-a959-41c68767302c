<?php

namespace App\Observers;

use App\Models\Product;
use App\Services\NotificationService;
use App\Enums\NotificationType;
use App\Enums\NotificationPriority;

class ProductObserver
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Handle the Product "created" event.
     */
    public function created(Product $product)
    {
        // Send notification to admin about new product
        if ($product->user_id !== 1) { // If not created by admin
            $this->notificationService->sendAdminNotification(
                title: "New Product Added",
                message: "A new product '{$product->name}' has been added by " . ($product->user->name ?? 'Unknown'),
                type: NotificationType::SYSTEM,
                priority: NotificationPriority::LOW,
                link: route('products.admin.edit', $product->id),
                linkText: 'View Product',
                subject: $product,
                userId: $product->user_id
            );
        }
    }

    /**
     * Handle the Product "updated" event.
     */
    public function updated(Product $product)
    {
        $changes = $product->getDirty();

        // Check if stock quantity changed and is low
        if (isset($changes['current_stock'])) {
            $previousStock = $product->getOriginal('current_stock');
            $currentStock = $product->current_stock;
            
            // Low stock alert
            if ($currentStock <= $product->low_stock_quantity && $currentStock > 0) {
                $this->notificationService->sendAdminNotification(
                    title: "Low Stock Alert",
                    message: "Product '{$product->name}' is running low on stock. Current stock: {$currentStock}",
                    type: NotificationType::STOCK,
                    priority: NotificationPriority::HIGH,
                    link: route('products.admin.edit', $product->id),
                    linkText: 'Update Stock',
                    subject: $product,
                    userId: $product->user_id
                );
            }
            
            // Out of stock alert
            if ($currentStock <= 0 && $previousStock > 0) {
                $this->notificationService->sendAdminNotification(
                    title: "Out of Stock Alert",
                    message: "Product '{$product->name}' is now out of stock!",
                    type: NotificationType::STOCK,
                    priority: NotificationPriority::URGENT,
                    link: route('products.admin.edit', $product->id),
                    linkText: 'Restock Product',
                    subject: $product,
                    userId: $product->user_id
                );
            }
            
            // Back in stock notification
            if ($currentStock > 0 && $previousStock <= 0) {
                $this->notificationService->sendAdminNotification(
                    title: "Product Back in Stock",
                    message: "Product '{$product->name}' is back in stock. Current stock: {$currentStock}",
                    type: NotificationType::STOCK,
                    priority: NotificationPriority::MEDIUM,
                    link: route('products.admin.edit', $product->id),
                    linkText: 'View Product',
                    subject: $product,
                    userId: $product->user_id
                );
            }
        }

        // Check if product was approved/disapproved
        if (isset($changes['approved']) && $product->getOriginal('approved') !== $product->approved) {
            if ($product->approved) {
                // Product approved
                if ($product->user) {
                    $this->notificationService->sendNotification(
                        user: $product->user,
                        title: "Product Approved",
                        message: "Your product '{$product->name}' has been approved and is now live on the marketplace.",
                        type: NotificationType::SYSTEM,
                        priority: NotificationPriority::HIGH,
                        link: "/products/{$product->slug}",
                        linkText: 'View Product',
                        subject: $product
                    );
                }
            } else {
                // Product disapproved
                if ($product->user) {
                    $this->notificationService->sendNotification(
                        user: $product->user,
                        title: "Product Needs Review",
                        message: "Your product '{$product->name}' requires some changes before approval.",
                        type: NotificationType::SYSTEM,
                        priority: NotificationPriority::MEDIUM,
                        link: "/seller/products",
                        linkText: 'Edit Product',
                        subject: $product
                    );
                }
            }
        }

        // Check if product was featured/unfeatured
        if (isset($changes['featured']) && $product->getOriginal('featured') !== $product->featured) {
            if ($product->featured && $product->user) {
                $this->notificationService->sendNotification(
                    user: $product->user,
                    title: "Product Featured",
                    message: "Congratulations! Your product '{$product->name}' is now featured.",
                    type: NotificationType::SYSTEM,
                    priority: NotificationPriority::HIGH,
                    link: "/products/{$product->slug}",
                    linkText: 'View Product',
                    subject: $product
                );
            }
        }
    }

    /**
     * Handle the Product "deleting" event.
     */
    public function deleting(Product $product)
    {
        // Notify admin about product deletion
        $this->notificationService->sendAdminNotification(
            title: "Product Deleted",
            message: "Product '{$product->name}' has been deleted from the marketplace",
            type: NotificationType::SYSTEM,
            priority: NotificationPriority::LOW,
            link: route('products.all'),
            linkText: 'View Products',
            subject: $product,
            userId: $product->user_id
        );
    }
} 