<?php

namespace App\Observers;

use App\Models\SupportTicket;
use App\Services\NotificationService;
use App\Enums\NotificationType;
use App\Enums\NotificationPriority;

class SupportTicketObserver
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Handle the SupportTicket "created" event.
     */
    public function created(SupportTicket $ticket)
    {
        // Send notification to admin when new support ticket is created
        $this->notificationService->sendAdminNotification(
            title: "New Support Ticket - #{$ticket->id}",
            message: "A new support ticket '{$ticket->subject}' has been created by {$ticket->user->name}",
            type: NotificationType::SUPPORT,
            priority: $this->getPriorityFromTicket($ticket),
            link: route('support_ticket.admin_show', $ticket->id),
            linkText: 'View Ticket',
            subject: $ticket,
            userId: $ticket->user_id
        );

        // Send confirmation notification to customer
        $this->notificationService->sendNotification(
            user: $ticket->user,
            title: "Support Ticket Created - #{$ticket->id}",
            message: "Your support ticket has been created successfully. Our team will respond shortly.",
            type: NotificationType::SUPPORT,
            priority: NotificationPriority::MEDIUM,
            link: "/support-tickets/{$ticket->id}",
            linkText: 'View Ticket',
            subject: $ticket
        );
    }

    /**
     * Handle the SupportTicket "updated" event.
     */
    public function updated(SupportTicket $ticket)
    {
        $changes = $ticket->getDirty();
        
        // Check if status changed
        if (isset($changes['status'])) {
            $previousStatus = $ticket->getOriginal('status');
            $newStatus = $ticket->status;
            
            // Send notification to customer about status change
            $this->notificationService->sendNotification(
                user: $ticket->user,
                title: "Ticket Status Updated - #{$ticket->id}",
                message: "Your support ticket status has been changed from {$previousStatus} to {$newStatus}",
                type: NotificationType::SUPPORT,
                priority: $newStatus === 'closed' ? NotificationPriority::LOW : NotificationPriority::MEDIUM,
                link: "/support-tickets/{$ticket->id}",
                linkText: 'View Ticket',
                subject: $ticket
            );

            // Send notification to admin about status change
            $this->notificationService->sendAdminNotification(
                title: "Ticket Status Updated - #{$ticket->id}",
                message: "Support ticket #{$ticket->id} status changed from {$previousStatus} to {$newStatus}",
                type: NotificationType::SUPPORT,
                priority: NotificationPriority::LOW,
                link: route('support_ticket.admin_show', $ticket->id),
                linkText: 'View Ticket',
                subject: $ticket,
                userId: $ticket->user_id
            );
        }

        // Check if priority changed
        if (isset($changes['priority'])) {
            $previousPriority = $ticket->getOriginal('priority');
            $newPriority = $ticket->priority;
            
            // Send notification to admin about priority change
            $this->notificationService->sendAdminNotification(
                title: "Ticket Priority Updated - #{$ticket->id}",
                message: "Support ticket #{$ticket->id} priority changed from {$previousPriority} to {$newPriority}",
                type: NotificationType::SUPPORT,
                priority: $this->getPriorityFromTicket($ticket),
                link: route('support_ticket.admin_show', $ticket->id),
                linkText: 'View Ticket',
                subject: $ticket,
                userId: $ticket->user_id
            );
        }
    }

    /**
     * Get notification priority based on ticket priority
     */
    private function getPriorityFromTicket(SupportTicket $ticket): string
    {
        return match(strtolower($ticket->priority ?? 'medium')) {
            'high', 'urgent' => NotificationPriority::HIGH,
            'low' => NotificationPriority::LOW,
            default => NotificationPriority::MEDIUM,
        };
    }
} 