<?php

namespace App\Models;

//use App\Models\Log;
use App\Models\Product;
use App\Models\ProductStock;
use App\Models\User;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Str;
use Auth;
use Carbon\Carbon;
use Storage;
use Illuminate\Support\Facades\Log;
//class ProductsUpdate implements ToModel, WithHeadingRow, WithValidation
class BulkProductsUpdate implements ToCollection, WithHeadingRow, WithValidation, ToModel
{
    private $rows = 0;

    public function collection(Collection $rows)
    {
        //dd($rows);

        foreach ($rows as $row) {
            if (isset($row['id'])) {
                $product = Product::findOrFail($row['id']);

    
                if ($product) {
                    //dd($row['current_stock']);
                    $product->update([
                    'name' => $row['name'],
                    'description' => $row['description'],
                    'short_description' => $row['short_description'],
                    'published'   => $row['published'],
                    'category_id' => $row['category_id'],
                    'brand_id' => $row['brand_id'],
                    'video_provider' => $row['video_provider'],
                    'video_link' => $row['video_link'],
                    'tags' => $row['tags'],
                    'unit_price' => $row['unit_price'],
                    'dropshipper_price'     => $row['dropshipper_price'],
                    'purchase_price' => $row['purchase_price'],
                    'current_stock' => $row['current_stock'],

                    //for addToCart js problem
                    'choice_options' => json_encode($row['choice_options'] ?? []),
                    'colors'         => json_encode($row['colors'] ?? []),
                    'variations'     => json_encode($row['variations'] ?? []),

                    'stock_visibility_state' => $row['stock_visibility_state'],
                    'cash_on_delivery'  => $row['cash_on_delivery'],
                    'featured'          => $row['featured'],
                    'seller_featured'   => $row['seller_featured'],

                    'unit'      => $row['unit'],
                    'weight'    => $row['weight'],
                    'length'    => $row['length'],
                    'width'     => $row['width'],
                    'height'    => $row['height'],
                    'allow_customer_review' => $row['allow_customer_review'],
                    'position'  => $row['position'],
                    'min_qty'   => $row['min_qty'],
                    'low_stock_quantity' => $row['low_stock_quantity'],
                    'discount'      => $row['discount'],
                    'discount_type' => $row['discount_type'],

                    'discount_start_date' => $row['discount_start_date'],
                    'discount_end_date'   => $row['discount_end_date'],


                    'tax'      => $row['tax'],
                    'tax_type' => $row['tax_type'],
                    'shipping_type' => $row['shipping_type'],
                    'shipping_cost' => $row['shipping_cost'],
                    'is_quantity_multiplied' => $row['is_quantity_multiplied'],

                    'meta_title' => $row['meta_title'],
                    'meta_description' => $row['meta_description'],
                    // 'meta_img' => $row['meta_img'],
                    'pdf'      => $row['pdf'],
                    'est_shipping_days' => $row['est_shipping_days'],
                    'slug' => preg_replace('/[^A-Za-z0-9\-]/', '', str_replace(' ', '-', strtolower($row['slug']))) . '-' . Str::random(5),
                    'thumbnail_img' => $this->downloadThumbnail($row['thumbnail_img']),
                    'photos' => $this->downloadGalleryImages($row['photos']),
                    'refundable' => $row['refundable'],
                    
                    'auction_product' => $row['auction_product'] ?? 0,

                    //'external_link'     => $row['external_link'],
                    //'external_link_btn' => $row['external_link_btn'],
                    'purchase_note' => $row['purchase_note'],
                    'shipping_class' => $row['shipping_class'],
                    'upsell'        => $row['upsell'],
                    'cross_sell'    => $row['cross_sell'],
                    'visibility_in_catalog' => $row['visibility_in_catalog'],
                    'grouped_product'       => $row['grouped_product'],
                    'backorders_allowed'    => $row['backorders_allowed'],
                    ]);
    
                    $product->ProductStock()->update([
                        'qty' => $row['current_stock'],
                        'price' => $row['unit_price'],
                        'sku' => $row['sku'],
                        'variant' => '',
                    ]);


                } else {
                    Log::error("Product with ID {$row['id']} not found during bulk update.");
                }


            } else {
                Log::error("ID field is missing in a row during bulk update.");
            }
        }
    
        flash(translate('Products updated successfully'))->success();
    }
    


    public function model(array $row)
    {
        ++$this->rows;
    }

    public function getRowCount(): int
    {
        return $this->rows;
    }

    public function rules(): array
    {
        return [
            // Can also use callback validation rules
            'unit_price' => function ($attribute, $value, $onFailure) {
                if (!is_numeric($value)) {
                    $onFailure('Unit price is not numeric');
                }
            }
        ];
    }

    public function downloadThumbnail($url)
    {
        try {
            $upload = new Upload;
            $upload->external_link = $url;
            $upload->type = 'image';
            $upload->save();

            return $upload->id;
        } catch (\Exception $e) {
        }
        return null;
    }

    public function downloadGalleryImages($urls)
    {
        $data = array();
        foreach (explode(',', str_replace(' ', '', $urls)) as $url) {
            $data[] = $this->downloadThumbnail($url);
        }
        return implode(',', $data);
    }
}
