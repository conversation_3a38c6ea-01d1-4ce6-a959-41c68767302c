<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class Cors_bk
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Handle preflight OPTIONS request
        if ($request->getMethod() === "OPTIONS") {
            return response()->json(['message' => 'CORS preflight'], 200, $this->getCorsHeaders($request));
        }

        \Log::info('CORS Middleware: Before calling next middleware', [
            'path' => $request->path(),
            'method' => $request->method()
        ]);

        try {
            $response = $next($request);

            \Log::info('CORS Middleware: After calling next middleware', [
                'path' => $request->path(),
                'response_type' => gettype($response),
                'response_class' => is_object($response) ? get_class($response) : 'not an object'
            ]);

        } catch (\Exception $e) {
            \Log::error('CORS Middleware: Exception caught', [
                'path' => $request->path(),
                'error' => $e->getMessage()
            ]);

            // Even if the request fails, we still need to add CORS headers
            $response = response()->json([
                'status' => 'error',
                'message' => 'Internal server error',
                'error' => $e->getMessage()
            ], 500);
        }

        // Add CORS headers to all responses (including error responses)
        // Check if response exists and has headers before setting them
        if ($response && $response->headers) {
            foreach ($this->getCorsHeaders($request) as $key => $value) {
                $response->headers->set($key, $value);
            }
        } else {
            // If response is null or invalid, create a new error response with CORS headers
            $debugInfo = [
                'response_is_null' => $response === null,
                'response_type' => gettype($response),
                'response_class' => is_object($response) ? get_class($response) : 'not an object',
                'has_headers' => is_object($response) && property_exists($response, 'headers'),
                'request_path' => $request->path(),
                'request_method' => $request->method()
            ];

            $response = response()->json([
                'status' => 'error',
                'message' => 'Invalid response',
                'debug' => $debugInfo
            ], 500, $this->getCorsHeaders($request));
        }
        $response->headers->set('Content-Security-Policy', "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://analytics.buzfi.com https://js.stripe.com https://*.stripe.com https://static.cloudflareinsights.com");
        return $response;
    }

    /**
     * Get CORS headers
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    private function getCorsHeaders(Request $request): array
    {
        $origin = $this->getAllowedOrigin($request);

        return [
            'Access-Control-Allow-Origin' => $origin,
            'Access-Control-Allow-Methods' => 'GET, POST, PUT, DELETE, OPTIONS, PATCH',
            'Access-Control-Allow-Headers' => 'Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN, Accept, Origin, X-Api-Key, X-Cart-Id, Cache-Control, Pragma, Expires, X-Temp-User-Id, X-Request-ID',
            'Access-Control-Allow-Credentials' => 'true',
            'Access-Control-Max-Age' => '86400',
            'Access-Control-Expose-Headers' => 'Authorization, X-Pagination-Count, X-Pagination-Page, X-Pagination-Limit',
        ];
    }

    /**
     * Get the allowed origin for the request
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string
     */
    private function getAllowedOrigin(Request $request)
    {
        $allowedOrigins = config('cors.allowed_origins', [
            'http://localhost:3000',
            'https://localhost:3000',
            'http://localhost/buzfi-main/buzfi',
            'https://buzfi.com',
            'https://www.buzfi.com',
            'http://buzfi.com',
            'https://iamnahid.me/',
            'https://api.iamnahid.me/',
            'http://www.buzfi.com',
            'http://127.0.0.1:3000',
            'https://127.0.0.1:3000',
        ]);

        $origin = $request->headers->get('Origin');

        // If no origin header, return the first allowed origin
        if (!$origin) {
            return $allowedOrigins[0] ?? 'http://localhost:3000';
        }

        if (in_array($origin, $allowedOrigins)) {
            return $origin;
        }

        // Check against patterns
        $patterns = config('cors.allowed_origins_patterns', []);
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $origin)) {
                return $origin;
            }
        }

        // For development, allow any localhost origin
        if (config('app.env') === 'development' && $origin) {
            if (preg_match('/^https?:\/\/(localhost|127\.0\.0\.1)/', $origin)) {
                return $origin;
            }
        }

        // Never return wildcard when credentials are enabled
        // Return the requesting origin if it's from localhost in development
        if (config('app.env') === 'development' && $origin &&
            (strpos($origin, 'localhost') !== false || strpos($origin, '127.0.0.1') !== false)) {
            return $origin;
        }

        // Default fallback to first allowed origin (never wildcard)
        return $allowedOrigins[0] ?? 'http://localhost:3000';
    }
}
