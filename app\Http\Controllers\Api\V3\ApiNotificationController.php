<?php

namespace App\Http\Controllers\Api\V3;

use App\Enums\NotificationPriority;
use App\Enums\NotificationType;
use App\Http\Controllers\Controller;
use App\Http\Resources\V3\Notifications\NotificationCountsResource;
use App\Http\Resources\V3\Notifications\NotificationPreferencesResource;
use App\Http\Resources\V3\Notifications\NotificationResource;
use App\Http\Resources\V3\Notifications\NotificationsCollection;
use App\Models\Notification;
use App\Models\UserNotification;
use App\Models\UserNotificationPreferences;
use App\Services\NotificationService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Helpers\NotificationHelper;

class ApiNotificationController extends ApiResponse
{
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
    }

    /**
     * Get user notifications (ONLY ADMIN-TRIGGERED)
     */
    public function getUserNotifications(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'nullable|integer|min:1',
            'limit' => 'nullable|integer|min:1|max:50',
            'unread_only' => 'nullable|boolean',
            'type' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Invalid input data',
                $validator->errors()->messages(),
                400
            );
        }

        $page = $request->input('page', 1);
        $limit = $request->input('limit', 20);
        $unreadOnly = $request->boolean('unread_only', false);
        $type = $request->input('type');

        // Use UserNotification model and filter by source = 'admin'
        $query = UserNotification::where('user_id', Auth::id())
            ->where('source', 'admin') // Only show admin-triggered notifications
            ->orderBy('created_at', 'desc');

        if ($unreadOnly) {
            $query->where('read', false);
        }

        if ($type) {
            $query->where('type', $type);
        }

        $notifications = $query->paginate($limit, ['*'], 'page', $page);

        return $this->success([
            'notifications' => $notifications->items(),
            'pagination' => [
                'current_page' => $notifications->currentPage(),
                'last_page' => $notifications->lastPage(),
                'per_page' => $notifications->perPage(),
                'total' => $notifications->total(),
                'has_more' => $notifications->hasMorePages()
            ],
            'unread_count' => UserNotification::where('user_id', Auth::id())
                ->where('source', 'admin') // Only count admin-triggered notifications
                ->where('read', false)
                ->count()
        ]);
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'notification_id' => 'required|string|exists:user_notifications,id'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Invalid notification ID',
                $validator->errors()->messages(),
                400
            );
        }

        $notification = UserNotification::where('id', $request->notification_id)
            ->where('user_id', Auth::id())
            ->where('source', 'admin') // Only allow marking admin-triggered notifications
            ->first();

        if (!$notification) {
            return $this->error(
                'NOT_FOUND',
                'Notification not found',
                404
            );
        }

        $notification->update([
            'read' => true,
            'read_at' => now()
        ]);

        return $this->success(
            ['message' => 'Notification marked as read'],
            'Notification marked as read'
        );
    }

    /**
     * Mark all notifications as read
     */
    public function markAllAsRead()
    {
        $count = UserNotification::where('user_id', Auth::id())
            ->where('source', 'admin') // Only mark admin-triggered notifications
            ->where('read', false)
            ->update([
                'read' => true,
                'read_at' => now()
            ]);

        return $this->success([
            'message' => "Marked {$count} notifications as read",
            'count' => $count
        ]);
    }

    /**
     * Get unread notifications count
     */
    public function getUnreadCount()
    {
        $count = UserNotification::where('user_id', Auth::id())
            ->where('source', 'admin') // Only count admin-triggered notifications
            ->where('read', false)
            ->count();

        return $this->success(['unread_count' => $count]);
    }

    /**
     * Delete notification
     */
    public function deleteNotification(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'notification_id' => 'required|string|exists:user_notifications,id'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Invalid notification ID',
                $validator->errors()->messages(),
                400
            );
        }

        $notification = UserNotification::where('id', $request->notification_id)
            ->where('user_id', Auth::id())
            ->where('source', 'admin') // Only allow deleting admin-triggered notifications
            ->first();

        if (!$notification) {
            return $this->error(
                'NOT_FOUND',
                'Notification not found',
                404
            );
        }

        $notification->delete();

        return $this->success(
            ['message' => 'Notification deleted successfully'],
            'Notification deleted'
        );
    }

    /**
     * Get notification preferences
     */
    public function getPreferences()
    {
        $user = Auth::user();
        $preferences = $user->notification_preferences ?? [
            'email_notifications' => true,
            'push_notifications' => true,
            'sms_notifications' => false,
            'order_updates' => true,
            'marketing_emails' => true,
            'support_notifications' => true
        ];

        return $this->success(['preferences' => $preferences]);
    }

    /**
     * Update notification preferences
     */
    public function updatePreferences(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'preferences' => 'required|array',
            'preferences.email_notifications' => 'boolean',
            'preferences.push_notifications' => 'boolean',
            'preferences.sms_notifications' => 'boolean',
            'preferences.order_updates' => 'boolean',
            'preferences.marketing_emails' => 'boolean',
            'preferences.support_notifications' => 'boolean'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Invalid preferences data',
                $validator->errors()->messages(),
                400
            );
        }

        $user = Auth::user();
        $user->notification_preferences = $request->input('preferences');
        $user->save();

        return $this->success(
            ['message' => 'Notification preferences updated successfully'],
            'Preferences updated'
        );
    }

    /**
     * Get a paginated list of notifications for the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $user = Auth::user();
        $perPage = min($request->input('per_page', 10), 50);

        $query = $user->notifications()->with('subject');
        //dd($query);

        // Validate request parameters
        $validator = Validator::make($request->all(), [
            'type' => 'nullable|string|in:' . NotificationType::toString(),
            'read' => 'nullable|boolean',
            'priority' => 'nullable|string|in:' . NotificationPriority::toString(),
            'search' => 'nullable|string|max:255',
            'from_date' => 'nullable|date_format:Y-m-d',
            'to_date' => 'nullable|date_format:Y-m-d|after_or_equal:from_date',
            'sort_by' => 'nullable|string|in:date,priority',
            'sort_dir' => 'nullable|string|in:asc,desc',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid parameters',
                $validator->errors()->messages(),
                422
            );
        }

        // Apply filters
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        if ($request->has('read')) {
            $query->where('read', $request->read);
        }

        if ($request->has('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('message', 'like', "%{$search}%");
            });
        }

        if ($request->has('from_date')) {
            $query->whereDate('created_at', '>=', $request->from_date);
        }

        if ($request->has('to_date')) {
            $query->whereDate('created_at', '<=', $request->to_date);
        }

        // Apply sorting
        $sortBy = $request->input('sort_by', 'date');
        $sortDir = $request->input('sort_dir', 'desc');

        if ($sortBy === 'date') {
            $query->orderBy('created_at', $sortDir);
        } elseif ($sortBy === 'priority') {
            // Custom ordering for priority (high, medium, low)
            $priorityOrder = $sortDir === 'asc'
                ? ['low', 'medium', 'high']
                : ['high', 'medium', 'low'];

            $query->orderByRaw("FIELD(priority, '" . implode("', '", $priorityOrder) . "')");
        }

        $notifications = $query->paginate($perPage);
        //dd($notifications);

        return $this->success(new NotificationsCollection($notifications));
    }

    /**
     * Get notification counts for the dashboard.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function count(): JsonResponse
    {
        $user = Auth::user();

        $total = $user->notifications()->count();
        $unread = $user->notifications()->where('read', false)->count();

        // Count by type
        $byType = $user->notifications()
            ->select('type', DB::raw('count(*) as count'))
            ->groupBy('type')
            ->pluck('count', 'type')
            ->toArray();

        // Ensure all types are represented in the response
        $allTypes = NotificationType::toArray();
        $byTypeComplete = [];

        foreach ($allTypes as $type) {
            $byTypeComplete[$type] = $byType[$type] ?? 0;
        }

        return $this->success(new NotificationCountsResource([
            'total' => $total,
            'unread' => $unread,
            'by_type' => $byTypeComplete
        ]));
    }

    /**
     * Get a specific notification by ID.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(string $id): JsonResponse
    {
        $user = Auth::user();
        $notification = $user->notifications()->find($id);

        if (!$notification) {
            return $this->error(
                'NOT_FOUND',
                'Notification not found',
                'The requested notification could not be found',
                404
            );
        }

        return $this->success(new NotificationResource($notification));
    }

    /**
     * Delete a notification.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(string $id): JsonResponse
    {
        $user = Auth::user();
        $notification = $user->notifications()->find($id);

        if (!$notification) {
            return $this->error(
                'NOT_FOUND',
                'Notification not found',
                'The requested notification could not be found',
                404
            );
        }

        $notification->delete();

        return $this->success([
            'success' => true,
            'message' => 'Notification deleted successfully'
        ]);
    }

    /**
     * Trigger notification from frontend
     */
    public function triggerNotification(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'action' => 'required|string',
                'data' => 'required|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $action = $request->input('action');
            $data = $request->input('data');
            $userId = auth()->id();

            // Use NotificationService to trigger frontend notification
            $notification = $this->notificationService->triggerFromFrontend($action, $data, $userId);

            if ($notification) {
                return response()->json([
                    'success' => true,
                    'message' => 'Notification triggered successfully',
                    'data' => [
                        'notification_id' => $notification->id,
                        'title' => $notification->title,
                        'message' => $notification->message,
                        'created_at' => $notification->created_at->toISOString()
                    ]
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to trigger notification'
            ], 500);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error triggering notification: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get real-time admin bell data
     */
    public function getAdminBellData()
    {
        try {
            $unreadCount = $this->notificationService->getAdminUnreadCount();
            $recentNotifications = $this->notificationService->getAdminRecentNotifications(10);
            
            // Check for urgent notifications
            $hasUrgent = collect($recentNotifications)->contains('priority', NotificationPriority::URGENT);

            return response()->json([
                'success' => true,
                'data' => [
                    'unread_count' => $unreadCount,
                    'recent_notifications' => $recentNotifications,
                    'has_urgent' => $hasUrgent,
                    'last_updated' => now()->toISOString()
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching admin bell data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send custom admin notification
     */
    public function sendAdminNotification(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'message' => 'required|string',
                'type' => 'sometimes|string',
                'priority' => 'sometimes|string',
                'link' => 'sometimes|string|nullable',
                'link_text' => 'sometimes|string|nullable',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $title = $request->input('title');
            $message = $request->input('message');
            $type = $request->input('type', NotificationType::SYSTEM);
            $priority = $request->input('priority', NotificationPriority::MEDIUM);
            $link = $request->input('link');
            $linkText = $request->input('link_text', 'View Details');
            $userId = auth()->id();

            // Send instant admin notification
            $notification = $this->notificationService->sendInstantAdminNotification(
                $title,
                $message,
                $type,
                $priority,
                $link,
                $linkText,
                $userId
            );

            return response()->json([
                'success' => true,
                'message' => 'Admin notification sent successfully',
                'data' => [
                    'notification_id' => $notification->id,
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'created_at' => $notification->created_at->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error sending admin notification: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send test notification
     */
    public function sendTestNotification(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'message' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $title = $request->input('title');
            $message = $request->input('message');
            $userId = auth()->id();

            // Send test notification
            $notification = $this->notificationService->sendInstantAdminNotification(
                "[TEST] " . $title,
                $message,
                NotificationType::SYSTEM,
                NotificationPriority::LOW,
                null,
                'Test Notification',
                $userId
            );

            return response()->json([
                'success' => true,
                'message' => 'Test notification sent successfully',
                'data' => [
                    'notification_id' => $notification->id,
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'created_at' => $notification->created_at->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error sending test notification: ' . $e->getMessage()
            ], 500);
        }
    }
}
