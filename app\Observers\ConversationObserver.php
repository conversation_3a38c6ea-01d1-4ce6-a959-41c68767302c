<?php

namespace App\Observers;

use App\Models\Conversation;
use App\Services\NotificationService;
use App\Enums\NotificationType;
use App\Enums\NotificationPriority;

class ConversationObserver
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Handle the Conversation "created" event.
     */
    public function created(Conversation $conversation)
    {
        // Send notification to admin when new message/conversation is created
        $this->notificationService->sendAdminNotification(
            title: "New Message Received",
            message: "A new message has been received from {$conversation->sender->name}",
            type: NotificationType::MESSAGE,
            priority: NotificationPriority::MEDIUM,
            link: route('conversations.admin_show', $conversation->id),
            linkText: 'View Conversation',
            subject: $conversation,
            userId: $conversation->sender_id
        );

        // Send notification to receiver if not admin
        if ($conversation->receiver && $conversation->receiver->user_type !== 'admin') {
            $this->notificationService->sendNotification(
                user: $conversation->receiver,
                title: "New Message",
                message: "You have received a new message from {$conversation->sender->name}",
                type: NotificationType::MESSAGE,
                priority: NotificationPriority::MEDIUM,
                link: "/messages/{$conversation->id}",
                linkText: 'View Message',
                subject: $conversation
            );
        }
    }

    /**
     * Handle the Conversation "updated" event.
     */
    public function updated(Conversation $conversation)
    {
        // If a new reply is added, notify relevant parties
        if ($conversation->isDirty('message') || $conversation->isDirty('updated_at')) {
            // Determine who should be notified based on who sent the message
            if ($conversation->sender_type === 'customer') {
                // Customer sent message, notify admin
                $this->notificationService->sendAdminNotification(
                    title: "Customer Reply",
                    message: "Customer {$conversation->sender->name} has replied to conversation",
                    type: NotificationType::MESSAGE,
                    priority: NotificationPriority::MEDIUM,
                    link: route('conversations.admin_show', $conversation->id),
                    linkText: 'View Reply',
                    subject: $conversation,
                    userId: $conversation->sender_id
                );
            } elseif ($conversation->sender_type === 'admin' && $conversation->receiver) {
                // Admin sent message, notify customer
                $this->notificationService->sendNotification(
                    user: $conversation->receiver,
                    title: "Admin Reply",
                    message: "You have received a reply from our support team",
                    type: NotificationType::MESSAGE,
                    priority: NotificationPriority::HIGH,
                    link: "/messages/{$conversation->id}",
                    linkText: 'View Reply',
                    subject: $conversation
                );
            }
        }
    }
} 