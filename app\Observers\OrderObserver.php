<?php

namespace App\Observers;

use App\Models\Order;
use App\Services\NotificationService;
use App\Enums\NotificationType;
use App\Enums\NotificationPriority;

class OrderObserver
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Handle the Order "created" event.
     */
    public function created(Order $order)
    {
        // Send notification to admin when new order is placed
        $this->notificationService->sendAdminNotification(
            title: "New Order Received - #{$order->code}",
            message: "A new order has been placed by {$order->user->name} worth " . single_price($order->grand_total),
            type: NotificationType::ORDER,
            priority: NotificationPriority::HIGH,
            link: route('all_orders.show', $order->code),
            linkText: 'View Order',
            subject: $order,
            userId: $order->user_id
        );

        // Send confirmation notification to customer
        $this->notificationService->sendNotification(
            user: $order->user,
            title: "Order Confirmed - #{$order->code}",
            message: "Your order has been placed successfully and is being processed.",
            type: NotificationType::ORDER,
            priority: NotificationPriority::HIGH,
            link: "/" . ($order->user->user_type === 'b2b' ? 'dropshipper' : $order->user->user_type) . "/orders/{$order->code}",
            linkText: 'View Order',
            subject: $order
        );
    }

    /**
     * Handle the Order "updated" event.
     */
    public function updated(Order $order)
    {
        $changes = $order->getDirty();
        
        // Check if delivery status changed
        if (isset($changes['delivery_status']) && $order->getOriginal('delivery_status') !== $order->delivery_status) {
            $previousStatus = $order->getOriginal('delivery_status');
            $newStatus = $order->delivery_status;
            
            // Send notification to customer about status change
            $this->notificationService->sendOrderStatusUpdate($order, $previousStatus, $newStatus);
            
            // Send notification to admin about status change
            $this->notificationService->sendAdminNotification(
                title: "Order Status Updated - #{$order->code}",
                message: "Order status changed from {$previousStatus} to {$newStatus} for customer {$order->user->name}",
                type: NotificationType::ORDER,
                priority: NotificationPriority::MEDIUM,
                link: route('all_orders.show', $order->code),
                linkText: 'View Order',
                subject: $order,
                userId: $order->user_id
            );
        }

        // Check if payment status changed
        if (isset($changes['payment_status']) && $order->getOriginal('payment_status') !== $order->payment_status) {
            $previousPaymentStatus = $order->getOriginal('payment_status');
            $newPaymentStatus = $order->payment_status;
            
            // Send notification to admin about payment status change
            $this->notificationService->sendAdminNotification(
                title: "Payment Status Updated - #{$order->code}",
                message: "Payment status changed from {$previousPaymentStatus} to {$newPaymentStatus} for order #{$order->id}",
                type: NotificationType::PAYMENT,
                priority: $newPaymentStatus === 'paid' ? NotificationPriority::HIGH : NotificationPriority::MEDIUM,
                link: route('all_orders.show', $order->code),
                linkText: 'View Order',
                subject: $order,
                userId: $order->user_id
            );

            // Send notification to customer about payment status
            if ($newPaymentStatus === 'paid') {
                $this->notificationService->sendNotification(
                    user: $order->user,
                    title: "Payment Confirmed - #{$order->code}",
                    message: "Your payment has been confirmed and your order is being processed.",
                    type: NotificationType::PAYMENT,
                    priority: NotificationPriority::HIGH,
                    link: "/" . ($order->user->user_type === 'b2b' ? 'dropshipper' : $order->user->user_type) . "/orders/{$order->code}",
                    linkText: 'View Order',
                    subject: $order
                );
            }
        }
    }

    /**
     * Handle the Order "deleting" event.
     */
    public function deleting(Order $order)
    {
        // Send notification to admin about order cancellation
        $this->notificationService->sendAdminNotification(
            title: "Order Cancelled - #{$order->code}",
            message: "Order #{$order->code} for customer {$order->user->name} has been cancelled",
            type: NotificationType::ORDER,
            priority: NotificationPriority::MEDIUM,
            link: route('all_orders.index'),
            linkText: 'View Orders',
            subject: $order,
            userId: $order->user_id
        );
    }
} 