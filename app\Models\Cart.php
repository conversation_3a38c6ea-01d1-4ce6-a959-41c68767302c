<?php

namespace App\Models;

use App\Models\User;
use App\Models\Address;
use Illuminate\Database\Eloquent\Model;

class Cart extends Model
{
    protected $guarded = [];
    protected $fillable = [
        'cart_info_id',
        'address_id',
        'price',
        'tax',
        'shipping_cost',
        'discount',
        'product_referral_code',
        'coupon_code',
        'coupon_applied',
        'quantity',
        'user_id',
        'temp_user_id',
        'owner_id',
        'product_id',
        'variation',
        'status',
        'original_price',
        'options',
        'gift_wrap',
        'gift_message',
        'last_modified',
        'added_by',
        'notes'
    ];
    
    protected $casts = [
        'quantity' => 'integer',
        'price' => 'float',
        'tax' => 'float',
        'shipping_cost' => 'float',
        'discount' => 'float',
        'original_price' => 'float',
        'coupon_applied' => 'boolean',
        'gift_wrap' => 'boolean',
        'options' => 'json',
        'last_modified' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function address()
    {
        return $this->belongsTo(Address::class);
    }
    
    public function cartInfo()
    {
        return $this->belongsTo(CartInfo::class, 'cart_info_id');
    }
    
    /**
     * Scope for active cart items
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }
    
    /**
     * Set the last_modified date before saving
     */
    protected static function boot()
    {
        parent::boot();
        
        static::saving(function ($model) {
            $model->last_modified = now();
        });
    }
}
