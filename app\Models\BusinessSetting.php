<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class BusinessSetting extends Model
{
    protected $guarded = [];
    // Set the table name if needed
    // protected $table = 'business_settings';

    /**
     * Boot method to listen for model events and reset cache.
     */
    protected static function boot()
    {
        parent::boot();

        // When a setting is created
        static::created(function ($setting) {
            Cache::forget('business_settings_array');
            Cache::forget('return_faq');
        });

        // When a setting is updated
        static::updated(function ($setting) {
            Cache::forget('return_faq');
            Cache::forget('business_settings_array');
        });

        // When a setting is deleted
        static::deleted(function ($setting) {
            Cache::forget('return_faq');
            Cache::forget('business_settings_array');
        });
    }
}
