<?php

namespace App\Models;

use Illuminate\Notifications\Notifiable;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Foundation\Auth\User as Authenticatable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use App\Models\Cart;
use App\Notifications\EmailVerificationNotification;
use <PERSON>tie\Permission\Traits\HasRoles;

class User extends Authenticatable implements MustVerifyEmail
{
    use Notifiable, HasApiTokens, HasRoles;

    public function sendEmailVerificationNotification()
    {
        $this->notify(new EmailVerificationNotification());
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'news_letter',
        'address',
        'city',
        'postal_code',
        'phone',
        'country',
        'provider_id',
        'email_verified_at',
        'verification_code'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    public function wishlists()
    {
        return $this->hasMany(Wishlist::class);
    }

    public function customer()
    {
        return $this->hasOne(Customer::class);
    }

    public function affiliate_user()
    {
        return $this->hasOne(AffiliateUser::class);
    }

    public function affiliate_withdraw_request()
    {
        return $this->hasMany(AffiliateWithdrawRequest::class);
    }

    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function shop()
    {
        return $this->hasOne(Shop::class);
    }
    public function seller()
    {
        return $this->hasOne(Seller::class);
    }


    public function staff()
    {
        return $this->hasOne(Staff::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function seller_orders()
    {
        return $this->hasMany(Order::class, "seller_id");
    }
    public function seller_sales()
    {
        return $this->hasMany(OrderDetail::class, "seller_id");
    }

    public function wallets()
    {
        return $this->hasMany(Wallet::class)->orderBy('created_at', 'desc');
    }

    public function club_point()
    {
        return $this->hasOne(ClubPoint::class);
    }

    public function customer_package()
    {
        return $this->belongsTo(CustomerPackage::class);
    }

    public function customer_package_payments()
    {
        return $this->hasMany(CustomerPackagePayment::class);
    }

    public function customer_products()
    {
        return $this->hasMany(CustomerProduct::class);
    }

    public function seller_package_payments()
    {
        return $this->hasMany(SellerPackagePayment::class);
    }

    public function carts()
    {
        return $this->hasMany(Cart::class);
    }

    public function cartInfo()
    {
        return $this->hasOne(CartInfo::class);
    }

    public function reviews()
    {
        return $this->hasMany(Review::class);
    }
    public function getApprovedUserRatingCount(): int
    {
        return $this->reviews()->whereNotNull('rating')->count();
    }
    public function getAverageRating(): float
    {
        return round($this->reviews()->whereNotNull('rating')->avg('rating'), 2);
    }
    public function addresses()
    {
        return $this->hasMany(Address::class);
    }

    public function affiliate_log()
    {
        return $this->hasMany(AffiliateLog::class);
    }

    public function product_bids()
    {
        return $this->hasMany(AuctionProductBid::class);
    }
    public function product_queries()
    {
        return $this->hasMany(ProductQuery::class, 'customer_id');
    }
    public function uploads()
    {
        return $this->hasMany(Upload::class);
    }
    public function stripeCustomer()
    {
        return $this->hasOne(StripeCustomer::class);
    }

    /**
     * Get the user's notifications.
     */
    public function notifications()
    {
        return $this->hasMany(UserNotification::class);
    }

    /**
     * Get the user's unread notifications.
     */
    public function unreadNotifications()
    {
        return $this->notifications()->where('read', false);
    }

    /**
     * Get the count of unread notifications.
     */
    public function getUnreadNotificationsCountAttribute()
    {
        return $this->unreadNotifications()->count();
    }

    /**
     * Mark all unread notifications as read.
     */
    public function markUnreadNotificationsAsRead()
    {
        return $this->unreadNotifications()->update([
            'read' => true,
            'read_at' => now()
        ]);
    }

    /**
     * Get the user's notification preferences.
     */
    public function notificationPreferences()
    {
        return $this->hasMany(UserNotificationPreferences::class);
    }

    /**
     * Get the user's general preferences.
     */
    public function preferences()
    {
        return $this->hasMany(UserPreference::class);
    }

    public function stripeCards()
    {
        return $this->hasMany(StripeCard::class);
    }

    /**
     * Check if user is a dropshipper
     *
     * @return bool
     */
    public function isDropshipper()
    {
        return $this->user_type === 'b2b' || $this->hasRole('b2b');
    }

    /**
     * Check if user is admin or staff
     *
     * @return bool
     */
    public function isAdmin()
    {
        return $this->user_type === 'admin' || $this->user_type === 'staff';
    }
}
