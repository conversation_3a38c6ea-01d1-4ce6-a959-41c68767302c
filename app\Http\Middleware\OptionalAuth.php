<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful;

class OptionalAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Try to authenticate the user if a token is provided
        $token = $request->bearerToken();
        
        // If no Bearer token, check cookies for auth_token
        if (!$token && $request->cookie('auth_token')) {
            $token = $request->cookie('auth_token') ?? $request->cookie('jwt');
        }
        
        Log::info('OptionalAuth middleware - Start', [
            'has_token' => !!$token,
            'token_preview' => $token ? substr($token, 0, 20) . '...' : null,
            'token_source' => $request->bearerToken() ? 'bearer' : ($request->cookie('auth_token') ? 'cookie' : 'none'),
            'path' => $request->path()
        ]);
        
        if ($token) {
            try {
                // Use Sanctum to attempt authentication
                $personalAccessToken = \Laravel\Sanctum\PersonalAccessToken::findToken($token);
                
                Log::info('OptionalAuth middleware - Token lookup', [
                    'token_found' => !!$personalAccessToken,
                    'token_id' => $personalAccessToken ? $personalAccessToken->id : null
                ]);
                
                if ($personalAccessToken) {
                    $user = $personalAccessToken->tokenable;
                    
                    Log::info('OptionalAuth middleware - User found', [
                        'user_id' => $user ? $user->id : null,
                        'user_name' => $user ? $user->name : null
                    ]);
                    
                    if ($user) {
                        // Set the authenticated user for the default guard
                        Auth::guard('sanctum')->setUser($user);
                        Auth::setUser($user);
                        
                        // Also set it on the request
                        $request->setUserResolver(function () use ($user) {
                            return $user;
                        });
                        
                        // Store the user in the request attributes for direct access
                        $request->attributes->set('authenticated_user', $user);
                        
                        Log::info('OptionalAuth middleware - User authenticated', [
                            'user_id' => $user->id,
                            'auth_check_after' => Auth::check(),
                            'auth_id_after' => Auth::id(),
                            'guard_check' => Auth::guard('sanctum')->check(),
                            'guard_id' => Auth::guard('sanctum')->id()
                        ]);
                    }
                }
            } catch (\Exception $e) {
                // If authentication fails, just continue as guest
                // Don't throw an error, just log it for debugging
                Log::info('OptionalAuth middleware - Authentication failed', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }
        
        Log::info('OptionalAuth middleware - Final state', [
            'auth_check' => Auth::check(),
            'auth_id' => Auth::id(),
            'guard_check' => Auth::guard('sanctum')->check(),
            'guard_id' => Auth::guard('sanctum')->id()
        ]);
        
        return $next($request);
    }
} 