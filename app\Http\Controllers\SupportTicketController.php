<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SupportTicket;
use App\Models\TicketMessage;
use App\Models\TicketAttachment;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use App\Models\TicketCategory;
use App\Mail\SupportMailManager;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Mail;
use App\Helpers\NotificationHelper;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SupportTicketController extends Controller
{
    public function __construct() {
        // Staff Permission Check
        $this->middleware(['permission:view_all_support_tickets'])->only([
            'admin_index', 
            'adminGetTickets',
            'adminGetTicket',
            'adminAddReply',
            'adminUpdateTicket'
        ]);
    }

    /**
     * Get all tickets for the current user
     * 
     * @return \Illuminate\Http\Response
     */
    public function getTickets(Request $request)
    {
        try {
            $user = Auth::user();
            $query = SupportTicket::with(['category', 'messages', 'attachments'])
                ->where('user_id', $user->id);
                
            // Apply filters if provided
            if ($request->has('status') && $request->status) {
                $query->where('status', $request->status);
            }

            if ($request->has('category_id') && $request->category_id) {
                $query->where('category_id', $request->category_id);
            }
            
            $tickets = $query->orderBy('created_at', 'desc')->paginate(10);
                
            return response()->json([
                'status' => 'success',
                'data' => $tickets
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching tickets: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'user_id' => Auth::id()
            ]);
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve tickets: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get a specific ticket with messages and attachments
     * 
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function getTicket($id)
    {
        try {
            $user = Auth::user();
            
            $ticket = SupportTicket::with([
                'category',
                'messages' => function($query) {
                    $query->orderBy('created_at', 'asc');
                },
                'messages.attachments',
                'attachments'
            ])->where('id', $id)
              ->where('user_id', $user->id)
              ->first();
            
            if (!$ticket) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Ticket not found or access denied'
                ], 404);
            }
            
            // Mark messages as read
            foreach ($ticket->messages as $message) {
                if (!$message->is_staff_reply && !$message->is_read) {
                    $message->is_read = true;
                    $message->save();
                }
            }
            
            return response()->json([
                'status' => 'success',
                'data' => $ticket
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching ticket: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'user_id' => Auth::id(),
                'ticket_id' => $id
            ]);
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve ticket details: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Create a new support ticket
     * 
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function createTicket(Request $request)
    {
        try {
            // Log initial request
            Log::info('Support ticket creation request started', [
                'user' => Auth::id(),
                'hasAttachments' => $request->hasFile('attachments')
            ]);
            
            $validator = Validator::make($request->all(), [
                'subject' => 'required|string|max:255',
                'message' => 'required|string',
                'category_id' => 'required|exists:ticket_categories,id',
                'priority' => 'sometimes|in:low,medium,high',
                'attachments' => 'sometimes|array',
                'attachments.*' => 'file|max:10240' // 10MB max per file
            ]);
            
            if ($validator->fails()) {
                Log::error('Support ticket creation validation failed', [
                    'errors' => $validator->errors()->messages()
                ]);
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()->messages()
                ], 422);
            }
            
            $user = Auth::user();
            
            // Begin transaction
            DB::beginTransaction();
            
            // Create the ticket
            $ticket = new SupportTicket();
            $ticket->user_id = $user->id;
            $ticket->category_id = $request->category_id;
            $ticket->subject = $request->subject;
            $ticket->description = $request->message;
            $ticket->status = 'open';
            $ticket->priority = $request->priority ?? 'medium';
            $ticket->assigned_to = null;
            $ticket->last_reply_at = now();
            $ticket->save();
            
            Log::info('Support ticket created', [
                'ticketId' => $ticket->id
            ]);
            
            // Create the first message
            $message = new TicketMessage();
            $message->ticket_id = $ticket->id;
            $message->user_id = $user->id;
            $message->message = $request->message;
            $message->is_read = true;
            $message->is_staff_reply = false;
            $message->save();
            
            Log::info('Initial message created', [
                'messageId' => $message->id
            ]);
            
            // Handle file uploads
            if ($request->hasFile('attachments')) {
                Log::info('Processing attachments for new ticket', [
                    'count' => count($request->file('attachments'))
                ]);
                
                foreach ($request->file('attachments') as $file) {
                    try {
                        $originalName = $file->getClientOriginalName();
                        $extension = $file->getClientOriginalExtension();
                        $fileName = pathinfo($originalName, PATHINFO_FILENAME);
                        $safeFileName = $fileName . '_' . time() . '.' . $extension;
                        
                        // Store the file in the ticket-attachments directory
                        $path = $file->storeAs('ticket-attachments', $safeFileName, 'public');
                        
                        Log::info('File stored successfully', [
                            'path' => $path,
                            'original_name' => $originalName
                        ]);
                        
                        // Create the attachment record
                        $attachment = new TicketAttachment();
                        $attachment->ticket_id = $ticket->id;
                        $attachment->message_id = $message->id;
                        $attachment->file_name = $originalName;
                        $attachment->file_path = '/storage/' . $path;
                        $attachment->file_size = $file->getSize();
                        $attachment->file_type = $file->getMimeType();
                        $attachment->uploaded_by = Auth::id();
                        $attachment->save();
                        
                        Log::info('Attachment record created', [
                            'attachmentId' => $attachment->id
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Error processing attachment for new ticket', [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                        throw $e;
                    }
                }
            }
            
            // Send notification
            try {
                $this->sendTicketNotification($ticket, true);
                
                // Notify using helper
                NotificationHelper::supportTicketCreated($ticket);
            } catch (\Exception $e) {
                Log::warning('Failed to send notification', [
                    'error' => $e->getMessage()
                ]);
                // Don't throw exception, just log it
            }
            
            // Commit transaction
            DB::commit();
            
            return response()->json([
                'status' => 'success',
                'message' => 'Support ticket created successfully',
                'data' => [
                    'ticket_id' => $ticket->id,
                    'ticket' => $ticket->load(['category', 'messages', 'attachments'])
                ]
            ], 201);
        } catch (\Exception $e) {
            // Rollback transaction
            DB::rollBack();
            
            Log::error('Support ticket creation error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create ticket: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Add a new message to an existing ticket
     * 
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function addMessage(Request $request)
    {
        try {
            // Log initial request
            Log::info('Adding message to ticket request started', [
                'user' => Auth::id(),
                'hasAttachments' => $request->hasFile('attachments')
            ]);
            
            $validator = Validator::make($request->all(), [
                'ticketId' => 'required|exists:support_tickets,id',
                'message' => 'sometimes|string',
                'attachments' => 'sometimes|array',
                'attachments.*' => 'file|max:10240' // 10MB max per file
            ]);
            
            if ($validator->fails()) {
                Log::error('Message validation failed', [
                    'errors' => $validator->errors()->messages()
                ]);
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()->messages()
                ], 422);
            }
            
            // If no message and no attachments, return error
            if (empty($request->message) && !$request->hasFile('attachments')) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Message or attachments are required'
                ], 422);
            }
            
            $user = Auth::user();
            $ticketId = $request->ticketId;
            
            // Check if ticket belongs to user
            $ticket = SupportTicket::where('id', $ticketId)
                ->where('user_id', $user->id)
                ->first();
                
            if (!$ticket) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Ticket not found or access denied'
                ], 404);
            }
            
            // Check if ticket is open
            if ($ticket->status !== 'open' && $ticket->status !== 'pending' && $ticket->status !== 'waiting_for_customer') {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Cannot add message to a closed or resolved ticket'
                ], 400);
            }
            
            // Begin transaction
            DB::beginTransaction();
            
            // Create the message
            $message = new TicketMessage();
            $message->ticket_id = $ticketId;
            $message->user_id = $user->id;
            $message->message = $request->message ?? '';
            $message->is_read = false;
            $message->is_staff_reply = false;
            $message->save();
            
            Log::info('Message created', [
                'messageId' => $message->id,
                'ticketId' => $ticketId
            ]);
            
            // Update ticket status
            $ticket->status = 'waiting_for_support';
            $ticket->last_reply_at = now();
            $ticket->save();
            
            // Handle file uploads
            if ($request->hasFile('attachments')) {
                Log::info('Processing attachments for message', [
                    'count' => count($request->file('attachments'))
                ]);
                
                foreach ($request->file('attachments') as $file) {
                    try {
                        $originalName = $file->getClientOriginalName();
                        $extension = $file->getClientOriginalExtension();
                        $fileName = pathinfo($originalName, PATHINFO_FILENAME);
                        $safeFileName = $fileName . '_' . time() . '.' . $extension;
                        
                        // Store the file in the ticket-attachments directory
                        $path = $file->storeAs('ticket-attachments', $safeFileName, 'public');
                        
                        Log::info('File stored successfully', [
                            'path' => $path,
                            'original_name' => $originalName
                        ]);
                        
                        // Create the attachment record
                        $attachment = new TicketAttachment();
                        $attachment->ticket_id = $ticketId;
                        $attachment->message_id = $message->id;
                        $attachment->file_name = $originalName;
                        $attachment->file_path = '/storage/' . $path;
                        $attachment->file_size = $file->getSize();
                        $attachment->file_type = $file->getMimeType();
                        $attachment->uploaded_by = Auth::id();
                        $attachment->save();
                        
                        Log::info('Attachment record created', [
                            'attachmentId' => $attachment->id
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Error processing attachment for message', [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                        throw $e;
                    }
                }
            }
            
            // Send notification
            try {
                $this->sendMessageNotification($ticket, $message, false);
            } catch (\Exception $e) {
                Log::warning('Failed to send message notification', [
                    'error' => $e->getMessage()
                ]);
                // Don't throw exception, just log it
            }
            
            // Commit transaction
            DB::commit();
            
            // Return the created message with attachments
            $message->load('attachments');
            
            return response()->json([
                'status' => 'success',
                'message' => 'Message added successfully',
                'data' => $message
            ]);
        } catch (\Exception $e) {
            // Rollback transaction
            DB::rollBack();
            
            Log::error('Error adding message to ticket', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to add message: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Close a ticket
     * 
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function closeTicket(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'id' => 'required|exists:support_tickets,id'
            ]);
            
            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()->messages()
                ], 422);
            }
            
            $user = Auth::user();
            $ticketId = $request->id;
            
            // Check if ticket belongs to user
            $ticket = SupportTicket::where('id', $ticketId)
                ->where('user_id', $user->id)
                ->first();
                
            if (!$ticket) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Ticket not found or access denied'
                ], 404);
            }
            
            // Check if ticket is already closed
            if ($ticket->status === 'closed') {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Ticket is already closed'
                ], 400);
            }
            
            // Begin transaction
            DB::beginTransaction();
            
            // Update ticket status
            $ticket->status = 'closed';
            $ticket->save();
            
            // Add system message
            $message = new TicketMessage();
            $message->ticket_id = $ticketId;
            $message->user_id = $user->id;
            $message->message = 'Ticket closed by customer';
            $message->is_read = true;
            $message->is_staff_reply = false;
            $message->save();
            
            // Commit transaction
            DB::commit();
            
            return response()->json([
                'status' => 'success',
                'message' => 'Ticket closed successfully',
                'data' => [
                    'ticket' => $ticket->load(['category', 'messages', 'attachments'])
                ]
            ]);
        } catch (\Exception $e) {
            // Rollback transaction
            DB::rollBack();
            
            Log::error('Error closing ticket', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to close ticket: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Reopen a closed ticket
     * 
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function reopenTicket(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'id' => 'required|exists:support_tickets,id'
            ]);
            
            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()->messages()
                ], 422);
            }
            
            $user = Auth::user();
            $ticketId = $request->id;
            
            // Check if ticket belongs to user
            $ticket = SupportTicket::where('id', $ticketId)
                ->where('user_id', $user->id)
                ->first();
                
            if (!$ticket) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Ticket not found or access denied'
                ], 404);
            }
            
            // Check if ticket is closed
            if ($ticket->status !== 'closed') {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Only closed tickets can be reopened'
                ], 400);
            }
            
            // Begin transaction
            DB::beginTransaction();
            
            // Update ticket status
            $ticket->status = 'open';
            $ticket->save();
            
            // Add system message
            $message = new TicketMessage();
            $message->ticket_id = $ticketId;
            $message->user_id = $user->id;
            $message->message = 'Ticket reopened by customer';
            $message->is_read = true;
            $message->is_staff_reply = false;
            $message->save();
            
            // Commit transaction
            DB::commit();
            
            return response()->json([
                'status' => 'success',
                'message' => 'Ticket reopened successfully',
                'data' => [
                    'ticket' => $ticket->load(['category', 'messages', 'attachments'])
                ]
            ]);
        } catch (\Exception $e) {
            // Rollback transaction
            DB::rollBack();
            
            Log::error('Error reopening ticket', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to reopen ticket: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $tickets = SupportTicket::where('user_id', Auth::user()->id)->orderBy('created_at', 'desc')->paginate(10);
        return view('frontend.user.support_ticket.index', compact('tickets'));
    }

    public function admin_index(Request $request)
    {
        $sort_search = null;
        $category_id = null;
        
        // Load tickets with necessary relationships to avoid null values
        $tickets = SupportTicket::with(['category', 'user', 'messages' => function($query) {
            $query->orderBy('created_at', 'desc');
        }])->orderBy('created_at', 'desc');

        if ($request->has('search') && $request->search != null) {
            $sort_search = $request->search;
            $tickets = $tickets->where(function($query) use ($sort_search) {
                $query->where('subject', 'like', '%'.$sort_search.'%')
                      ->orWhere('id', 'like', '%'.$sort_search.'%');
            });
        }

        if ($request->has('category_id') && $request->category_id != null) {
            $category_id = $request->category_id;
            $tickets = $tickets->where('category_id', $category_id);
        }

        $tickets = $tickets->paginate(15);
        $categories = TicketCategory::where('is_active', 1)->get() ?? collect([]);

        // Map ticketreplies relationship to messages for backward compatibility
        foreach ($tickets as $ticket) {
            $ticket->ticketreplies = $ticket->messages ?? collect([]);
        }

        return view('backend.support.support_tickets.index', compact('tickets', 'sort_search', 'categories', 'category_id'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }



    public function send_support_mail_to_admin($ticket){
        $array['view'] = 'emails.support';
        $array['subject'] = translate('Support ticket Code is').':- '.$ticket->code;
        $array['from'] = env('MAIL_FROM_ADDRESS');
        $array['content'] = translate('Hi. A ticket has been created. Please check the ticket.');
        $array['link'] = route('support_ticket.admin_show', encrypt($ticket->id));
        $array['sender'] = $ticket->user->name;
        $array['details'] = $ticket->details;

        // dd($array);
        // dd(User::where('user_type', 'admin')->first()->email);
        try {
            Mail::to(User::where('user_type', 'admin')->first()->email)->queue(new SupportMailManager($array));
        } catch (\Exception $e) {
            // dd($e->getMessage());
        }
    }

    public function send_support_reply_email_to_user($ticket, $tkt_reply){
        $array['view'] = 'emails.support';
        $array['subject'] = translate('Support ticket Code is').':- '.$ticket->code;
        $array['from'] = env('MAIL_FROM_ADDRESS');
        $array['content'] = translate('Hi. A ticket has been created. Please check the ticket.');
        $array['link'] = $ticket->user->user_type == 'seller' ? route('seller.support_ticket.show', encrypt($ticket->id)) : route('support_ticket.show', encrypt($ticket->id));
        $array['sender'] = $tkt_reply->user->name;
        $array['details'] = $tkt_reply->reply;

        try {
            Mail::to($ticket->user->email)->queue(new SupportMailManager($array));
        } catch (\Exception $e) {
            //dd($e->getMessage());
        }
    }

    public function admin_store(Request $request)
    {
        $ticket_reply = new TicketReply;
        $ticket_reply->ticket_id = $request->ticket_id;
        $ticket_reply->user_id = Auth::user()->id;
        $ticket_reply->reply = $request->reply;
        $ticket_reply->files = $request->attachments;
        $ticket_reply->ticket->client_viewed = 0;
        $ticket_reply->ticket->status = $request->status;
        $ticket_reply->ticket->save();

        if($ticket_reply->save()){
            flash(translate('Reply has been sent successfully'))->success();
            $this->send_support_reply_email_to_user($ticket_reply->ticket, $ticket_reply);
            return back();
        }
        else{
            flash(translate('Something went wrong'))->error();
        }
    }

    public function seller_store(Request $request)
    {
        $ticket_reply = new TicketReply;
        $ticket_reply->ticket_id = $request->ticket_id;
        $ticket_reply->user_id = $request->user_id;
        $ticket_reply->reply = $request->reply;
        $ticket_reply->files = $request->attachments;
        $ticket_reply->ticket->viewed = 0;
        $ticket_reply->ticket->status = 'pending';
        $ticket_reply->ticket->save();
        if($ticket_reply->save()){

            flash(translate('Reply has been sent successfully'))->success();
            return back();
        }
        else{
            flash(translate('Something went wrong'))->error();
        }
    }



    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    /**
     * Get categories for support tickets
     * 
     * @return \Illuminate\Http\Response
     */
    public function getCategories()
    {
        try {
            $categories = TicketCategory::where('is_active', 1)->get();
            
            return response()->json([
                'status' => 'success',
                'data' => $categories
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching ticket categories', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch categories: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get ticket statistics for a user
     * 
     * @return \Illuminate\Http\Response
     */
    public function getTicketStatistics()
    {
        try {
            $userId = Auth::id();
            
            $statistics = [
                'total' => SupportTicket::where('user_id', $userId)->count(),
                'open' => SupportTicket::where('user_id', $userId)->where('status', 'open')->count(),
                'waiting_for_support' => SupportTicket::where('user_id', $userId)->where('status', 'waiting_for_support')->count(),
                'waiting_for_customer' => SupportTicket::where('user_id', $userId)->where('status', 'waiting_for_customer')->count(),
                'closed' => SupportTicket::where('user_id', $userId)->where('status', 'closed')->count(),
                'recent_tickets' => SupportTicket::with(['category', 'messages'])
                    ->where('user_id', $userId)
                    ->latest()
                    ->take(5)
                    ->get()
            ];
    
            return response()->json(['status' => 'success', 'data' => $statistics]);
        } catch (\Exception $e) {
            Log::error('Error fetching ticket statistics', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch ticket statistics: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Admin Methods
     */
    
    /**
     * Admin: Get all tickets with filtering
     * 
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function adminGetTickets(Request $request)
    {
        try {
            // Check permission
            if (!auth()->user()->can('view_all_support_tickets')) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Permission denied'
                ], 403);
            }
            
            $query = SupportTicket::with(['category', 'user', 'messages' => function($query) {
                $query->orderBy('created_at', 'desc');
            }])->orderBy('created_at', 'desc');
                
            // Apply filters
            if ($request->has('status') && $request->status) {
                $query->where('status', $request->status);
            }
            
            if ($request->has('category_id') && $request->category_id) {
                $query->where('category_id', $request->category_id);
            }
            
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('id', 'like', "%{$search}%")
                      ->orWhere('subject', 'like', "%{$search}%")
                      ->orWhereHas('user', function($userQuery) use ($search) {
                          $userQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('email', 'like', "%{$search}%");
                      });
                });
            }
            
            $tickets = $query->paginate($request->per_page ?? 15);
            
            return response()->json([
                'status' => 'success',
                'data' => $tickets
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching admin tickets', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'admin_id' => Auth::id()
            ]);
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve tickets: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Admin: Get a specific ticket with messages and attachments
     * 
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function adminGetTicket($id)
    {
        try {
            // Check permission
            if (!auth()->user()->can('view_all_support_tickets')) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Permission denied'
                ], 403);
            }
            
            $ticket = SupportTicket::with([
                'category',
                'user',
                'messages' => function($query) {
                    $query->orderBy('created_at', 'asc');
                },
                'messages.attachments',
                'attachments'
            ])->where('id', $id)->first();
            
            if (!$ticket) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Ticket not found'
                ], 404);
            }
            
            // Mark messages as read for admin
            foreach ($ticket->messages as $message) {
                if ($message->is_staff_reply == false && $message->is_read == false) {
                    $message->is_read = true;
                    $message->save();
                }
            }
            
            // Update ticket viewed status
            $ticket->viewed = 1;
            $ticket->save();
            
            return response()->json([
                'status' => 'success',
                'data' => $ticket
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching admin ticket', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'admin_id' => Auth::id(),
                'ticket_id' => $id
            ]);
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve ticket details: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Admin: Add reply to a ticket
     * 
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function adminAddReply(Request $request)
    {
        try {
            // Check permission
            if (!auth()->user()->can('view_all_support_tickets')) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Permission denied'
                ], 403);
            }
            
            $validator = Validator::make($request->all(), [
                'ticketId' => 'required|exists:support_tickets,id',
                'message' => 'sometimes|string',
                'status' => 'sometimes|in:open,pending,closed,resolved,waiting_for_customer',
                'attachments' => 'sometimes|array',
                'attachments.*' => 'file|max:10240' // 10MB max per file
            ]);
            
            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()->messages()
                ], 422);
            }
            
            // If no message and no attachments, return error
            if (empty($request->message) && !$request->hasFile('attachments')) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Message or attachments are required'
                ], 422);
            }
            
            $user = Auth::user();
            $ticketId = $request->ticketId;
            
            // Get ticket
            $ticket = SupportTicket::findOrFail($ticketId);
            
            // Begin transaction
            DB::beginTransaction();
            
            // Create the message
            $message = new TicketMessage();
            $message->ticket_id = $ticketId;
            $message->user_id = $user->id;
            $message->message = $request->message ?? '';
            $message->is_read = true;
            $message->is_staff_reply = true;
            $message->save();
            
            // Update ticket status
            if ($request->has('status')) {
                $ticket->status = $request->status;
            } else {
                $ticket->status = 'waiting_for_customer';
            }
            
            // Update ticket
            $ticket->client_viewed = 0; // Mark as unread for client
            $ticket->last_reply_at = now();
            $ticket->save();
            
            // Handle file uploads
            if ($request->hasFile('attachments')) {
                foreach ($request->file('attachments') as $file) {
                    try {
                        $originalName = $file->getClientOriginalName();
                        $extension = $file->getClientOriginalExtension();
                        $fileName = pathinfo($originalName, PATHINFO_FILENAME);
                        $safeFileName = $fileName . '_' . time() . '.' . $extension;
                        
                        // Store the file in the ticket-attachments directory
                        $path = $file->storeAs('ticket-attachments', $safeFileName, 'public');
                        
                        // Create the attachment record
                        $attachment = new TicketAttachment();
                        $attachment->ticket_id = $ticketId;
                        $attachment->message_id = $message->id;
                        $attachment->file_name = $originalName;
                        $attachment->file_path = '/storage/' . $path;
                        $attachment->file_size = $file->getSize();
                        $attachment->file_type = $file->getMimeType();
                        $attachment->uploaded_by = Auth::id();
                        $attachment->save();
                    } catch (\Exception $e) {
                        Log::error('Error processing attachment for admin reply', [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                        throw $e;
                    }
                }
            }
            
            // Send notification
            try {
                $this->sendMessageNotification($ticket, $message, true);
            } catch (\Exception $e) {
                Log::warning('Failed to send admin message notification', [
                    'error' => $e->getMessage()
                ]);
                // Don't throw exception, just log it
            }
            
            // Commit transaction
            DB::commit();
            
            // Return the created message with attachments
            $message->load('attachments');
            
            return response()->json([
                'status' => 'success',
                'message' => 'Reply added successfully',
                'data' => $message
            ]);
        } catch (\Exception $e) {
            // Rollback transaction
            DB::rollBack();
            
            Log::error('Error adding admin reply to ticket', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to add reply: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Admin: Update ticket status
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function adminUpdateTicket(Request $request, $id)
    {
        try {
            // Check permission
            if (!auth()->user()->can('view_all_support_tickets')) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Permission denied'
                ], 403);
            }
            
            $validator = Validator::make($request->all(), [
                'status' => 'sometimes|in:open,pending,closed,resolved,waiting_for_customer',
                'priority' => 'sometimes|in:low,medium,high,urgent',
                'assigned_to' => 'sometimes|nullable|exists:users,id'
            ]);
            
            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()->messages()
                ], 422);
            }
            
            $ticket = SupportTicket::findOrFail($id);
            
            // Begin transaction
            DB::beginTransaction();
            
            $originalStatus = $ticket->status;
            
            // Update ticket
            if ($request->has('status')) {
                $ticket->status = $request->status;
            }
            
            if ($request->has('priority')) {
                $ticket->priority = $request->priority;
            }
            
            if ($request->has('assigned_to')) {
                $ticket->assigned_to = $request->assigned_to;
            }
            
            $ticket->save();
            
            // Add system message for status change if status was changed
            if ($request->has('status') && $request->status != $originalStatus) {
                $message = new TicketMessage();
                $message->ticket_id = $id;
                $message->user_id = Auth::user()->id;
                $message->message = 'Ticket status changed to ' . $request->status;
                $message->is_read = true;
                $message->is_staff_reply = true;
                $message->save();
                
                // Send notification to customer about status change
                try {
                    $this->sendStatusChangeNotification($ticket, $originalStatus, $request->status);
                } catch (\Exception $e) {
                    Log::warning('Failed to send status change notification', [
                        'error' => $e->getMessage()
                    ]);
                }
            }
            
            // Commit transaction
            DB::commit();
            
            return response()->json([
                'status' => 'success',
                'message' => 'Ticket updated successfully',
                'data' => $ticket->load(['category', 'user', 'messages', 'attachments'])
            ]);
        } catch (\Exception $e) {
            // Rollback transaction
            DB::rollBack();
            
            Log::error('Error updating ticket', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update ticket: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Display the admin detail page for a ticket
     * 
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function admin_show($id)
    {
        try {
            $ticket = SupportTicket::with([
                'category',
                'user',
                'messages' => function($query) {
                    $query->orderBy('created_at', 'asc');
                },
                'messages.attachments',
                'attachments'
            ])->findOrFail(decrypt($id));
            
            // Map ticketreplies relationship to messages for backward compatibility
            $ticket->ticketreplies = $ticket->messages ?? collect([]);
            
            // Update viewed status
            $ticket->viewed = 1;
            $ticket->save();
            
            return view('backend.support.support_tickets.show', compact('ticket'));
        } catch (\Exception $e) {
            flash(translate('Error retrieving ticket details'))->error();
            return redirect()->route('support_ticket.admin_index');
        }
    }
    
    /**
     * Send notification for new ticket
     * 
     * @param SupportTicket $ticket
     * @param bool $isNewTicket
     * @return void
     */
    private function sendTicketNotification($ticket, $isNewTicket = true)
    {
        // Implementation depends on your notification system
        // This is a placeholder
        $admins = User::where('user_type', 'admin')->get();
        
        foreach ($admins as $admin) {
            // Send email notification
            try {
                $array['view'] = 'emails.support';
                $array['subject'] = $isNewTicket ? 'New Support Ticket #' . $ticket->id : 'New Message in Ticket #' . $ticket->id;
                $array['from'] = env('MAIL_FROM_ADDRESS');
                $array['content'] = $isNewTicket ? 'A new support ticket has been created.' : 'A new message has been added to a support ticket.';
                $array['link'] = route('support_ticket.admin_show', encrypt($ticket->id));
                $array['sender'] = Auth::user()->name;
                $array['details'] = $isNewTicket ? $ticket->description : '';
                
                Mail::to($admin->email)->queue(new SupportMailManager($array));
            } catch (\Exception $e) {
                Log::error('Failed to send support email: ' . $e->getMessage());
            }
        }
    }
    
    /**
     * Send notification for new message
     * 
     * @param SupportTicket $ticket
     * @param TicketMessage $message
     * @param bool $isStaffReply
     * @return void
     */
    private function sendMessageNotification($ticket, $message, $isStaffReply)
    {
        if ($isStaffReply) {
            // Notify customer
            $user = User::find($ticket->user_id);
            
            try {
                $array['view'] = 'emails.support';
                $array['subject'] = 'New Staff Reply to Ticket #' . $ticket->id;
                $array['from'] = env('MAIL_FROM_ADDRESS');
                $array['content'] = 'A support staff member has replied to your ticket.';
                $array['link'] = route('support_ticket.show', encrypt($ticket->id));
                $array['sender'] = 'Support Team';
                $array['details'] = $message->message;
                
                Mail::to($user->email)->queue(new SupportMailManager($array));
            } catch (\Exception $e) {
                Log::error('Failed to send customer notification: ' . $e->getMessage());
            }
        } else {
            // Notify staff
            $this->sendTicketNotification($ticket, false);
        }
    }
    
    /**
     * Send notification for status change
     * 
     * @param SupportTicket $ticket
     * @param string $oldStatus
     * @param string $newStatus
     * @return void
     */
    private function sendStatusChangeNotification($ticket, $oldStatus, $newStatus)
    {
        $user = User::find($ticket->user_id);
        
        try {
            $array['view'] = 'emails.support';
            $array['subject'] = 'Status Update for Ticket #' . $ticket->id;
            $array['from'] = env('MAIL_FROM_ADDRESS');
            $array['content'] = 'The status of your support ticket has been updated.';
            $array['link'] = route('support_ticket.show', encrypt($ticket->id));
            $array['sender'] = 'Support Team';
            $array['details'] = "Ticket status has been changed from '{$oldStatus}' to '{$newStatus}'.";
            
            Mail::to($user->email)->queue(new SupportMailManager($array));
        } catch (\Exception $e) {
            Log::error('Failed to send status change notification: ' . $e->getMessage());
        }
    }


    /**
     * Store a newly created ticket through traditional form
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        try {
            DB::beginTransaction();
            
            $ticket = new SupportTicket;
            $ticket->code = strtotime(date('Y-m-d H:i:s')).Auth::user()->id;
            $ticket->user_id = Auth::user()->id;
            $ticket->subject = $request->subject;
            $ticket->description = $request->details;
            $ticket->category_id = $request->category_id;
            $ticket->status = 'open';
            $ticket->priority = $request->priority ?? 'medium';
            $ticket->save();
            
            // Create initial message
            $message = new TicketMessage();
            $message->ticket_id = $ticket->id;
            $message->user_id = Auth::user()->id;
            $message->message = $request->details;
            $message->is_staff_reply = false;
            $message->is_read = true;
            $message->save();
            
            // Handle attachments
            if ($request->hasFile('attachments')) {
                foreach ($request->file('attachments') as $file) {
                    $originalName = $file->getClientOriginalName();
                    $extension = $file->getClientOriginalExtension();
                    $fileName = pathinfo($originalName, PATHINFO_FILENAME);
                    $safeFileName = $fileName . '_' . time() . '.' . $extension;
                    
                    // Store the file
                    $path = $file->storeAs('ticket-attachments', $safeFileName, 'public');
                    
                    // Create attachment record
                    $attachment = new TicketAttachment();
                    $attachment->ticket_id = $ticket->id;
                    $attachment->message_id = $message->id;
                    $attachment->file_name = $originalName;
                    $attachment->file_path = '/storage/' . $path;
                    $attachment->file_size = $file->getSize();
                    $attachment->file_type = $file->getMimeType();
                    $attachment->uploaded_by = Auth::id();
                    $attachment->save();
                }
            }
            
            // Send notifications
            $this->sendTicketNotification($ticket);
            
            // Use notification helper
            NotificationHelper::supportTicketCreated($ticket);
            
            DB::commit();
            
            flash(translate('Ticket has been sent successfully'))->success();
            return redirect()->route('support_ticket.index');
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Error creating ticket: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            
            flash(translate('Something went wrong'))->error();
            return back();
        }
    }

    /**
     * Display the specified ticket
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        try {
            $ticket = SupportTicket::with([
                'category',
                'messages' => function($query) {
                    $query->orderBy('created_at', 'asc');
                },
                'messages.attachments',
                'attachments'
            ])->findOrFail(decrypt($id));
            
            // Verify ownership
            if ($ticket->user_id != Auth::id()) {
                flash(translate('You do not have permission to view this ticket'))->error();
                return redirect()->route('support_ticket.index');
            }
            
            // Mark as viewed by client
            $ticket->client_viewed = 1;
            $ticket->save();
            
            // Map for backward compatibility
            $ticket->ticketreplies = $ticket->messages;
            
            return view('frontend.user.support_ticket.show', compact('ticket'));
        } catch (\Exception $e) {
            flash(translate('Error retrieving ticket details'))->error();
            return redirect()->route('support_ticket.index');
        }
    }
}
