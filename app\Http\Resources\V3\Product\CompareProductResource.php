<?php

namespace App\Http\Resources\V3\Product;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\V3\Product\ProductOftenBuyTogetherResource;
use App\Http\Resources\V3\Product\ProductVariantsResource;
use App\Http\Resources\V3\Product\ProductVatTaxesResource;
use App\Http\Resources\V3\Product\ProductVideosResource;
use App\Http\Resources\V3\Product\ReviewResource;
use App\Http\Resources\V3\Product\SkuResource;
use App\Models\Attribute;
use App\Models\Product;
use Illuminate\Support\Facades\Log;

class CompareProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'product' => new ProductResource($this->product),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
