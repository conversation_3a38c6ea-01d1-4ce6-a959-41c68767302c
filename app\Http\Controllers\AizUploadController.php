<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Upload;
use Illuminate\Support\Facades\Log;
use Response;
use Auth;
use Illuminate\Support\Facades\Storage;
use Image;
use enshrined\svgSanitize\Sanitizer;

class AizUploadController extends Controller
{
    public function index(Request $request)
    {

        $all_uploads = (auth()->user()->user_type == 'seller') ? Upload::where('user_id', auth()->user()->id) : Upload::query();
        $search = null;
        $sort_by = null;

        if ($request->search != null) {
            $search = $request->search;
            $all_uploads->where('file_original_name', 'like', '%' . $request->search . '%');
        }

        $sort_by = $request->sort;
        switch ($request->sort) {
            case 'newest':
                $all_uploads->orderBy('created_at', 'desc');
                break;
            case 'oldest':
                $all_uploads->orderBy('created_at', 'asc');
                break;
            case 'smallest':
                $all_uploads->orderBy('file_size', 'asc');
                break;
            case 'largest':
                $all_uploads->orderBy('file_size', 'desc');
                break;
            default:
                $all_uploads->orderBy('created_at', 'desc');
                break;
        }

        $all_uploads = $all_uploads->paginate(60)->appends(request()->query());
        Log::error('All Uploads: ' . print_r($all_uploads, true));

        return (auth()->user()->user_type == 'seller')
            ? view('seller.uploads.index', compact('all_uploads', 'search', 'sort_by'))
            : view('backend.uploaded_files.index', compact('all_uploads', 'search', 'sort_by'));
    }

    public function create()
    {
        return (auth()->user()->user_type == 'seller')
            ? view('seller.uploads.create')
            : view('backend.uploaded_files.create');
    }


    public function show_uploader(Request $request)
    {
        return view('uploader.aiz-uploader');
    }
    public function upload(Request $request)
    {
        $type = [
            "jpg" => "image", "jpeg" => "image", "png" => "image", "svg" => "image", "webp" => "image", "gif" => "image",
            "mp4" => "video", "mpg" => "video", "mpeg" => "video", "webm" => "video", "ogg" => "video", "avi" => "video",
            "mov" => "video", "flv" => "video", "swf" => "video", "mkv" => "video", "wmv" => "video",
            "wma" => "audio", "aac" => "audio", "wav" => "audio", "mp3" => "audio",
            "zip" => "archive", "rar" => "archive", "7z" => "archive",
            "doc" => "document", "txt" => "document", "docx" => "document", "pdf" => "document",
            "csv" => "document", "xml" => "document", "ods" => "document", "xlr" => "document",
            "xls" => "document", "xlsx" => "document"
        ];

        if (!$request->hasFile('aiz_file') || !$request->file('aiz_file')->isValid()) {
            return response()->json(['error' => 'Invalid file'], 400);
        }

        $file = $request->file('aiz_file');
        $extension = strtolower($file->getClientOriginalExtension());

        if (!isset($type[$extension])) {
            return response()->json(['error' => 'Unsupported file type'], 400);
        }

        $upload = new Upload;
        $upload->file_original_name = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $upload->extension = $extension;
        $upload->type = $type[$extension];
        $upload->user_id = Auth::id();

        $fileContent = null;
        $size = $file->getSize();

        // Handle SVG sanitization
        if ($extension === 'svg') {
            try {
                $sanitizer = new \enshrined\svgSanitize\Sanitizer();
                $dirtySVG = file_get_contents($file->getRealPath());
                $cleanSVG = $sanitizer->sanitize($dirtySVG);
                $fileContent = $cleanSVG;
                $size = strlen($cleanSVG);
            } catch (\Exception $e) {
                \Log::error('SVG sanitization failed: ' . $e->getMessage());
                return response()->json(['error' => 'SVG sanitization failed'], 500);
            }
        }

        // Handle image optimization (except SVG)
        if ($upload->type === 'image' && get_setting('disable_image_optimization') != 1 && $extension !== 'svg') {
            try {
                $img = \Image::make($file->getRealPath())->encode();
                $height = $img->height();
                $width = $img->width();

                if ($width > $height && $width > 1500) {
                    $img->resize(1500, null, function ($constraint) {
                        $constraint->aspectRatio();
                    });
                } elseif ($height > 1500) {
                    $img->resize(null, 800, function ($constraint) {
                        $constraint->aspectRatio();
                    });
                }

                $fileContent = (string) $img->encode();
                $size = strlen($fileContent);
            } catch (\Exception $e) {
                \Log::error('Image optimization failed: ' . $e->getMessage());
                $fileContent = file_get_contents($file->getRealPath());
            }
        }

        // Fallback if not SVG or image
        if ($fileContent === null) {
            $fileContent = file_get_contents($file->getRealPath());
        }

        // Final storage path
        $fileName = uniqid() . '.' . $extension;
        $path = "nahid/all/{$fileName}";

        // Upload directly to Spaces
        Storage::disk('spaces')->put($path, $fileContent, [
            'visibility' => 'public',
            'ContentType' => $extension === 'svg' ? 'image/svg+xml' : $file->getMimeType()
        ]);
       /* Storage::disk('spaces')->put($path, $fileContent, [
            'visibility' => 'public',
            'ContentType' => $extension === 'svg' ? 'image/svg+xml' : $file->getMimeType(),
            'CacheControl' => 'public, max-age=31536000, immutable',
        ]);*/




        $upload->file_name = $path;
        $upload->file_size = $size;
        $upload->save();

        return response()->json(['success' => true, 'path' => $path], 200);
    }

    public function upload_bk(Request $request)
    {
        $type = array(
            "jpg" => "image",
            "jpeg" => "image",
            "png" => "image",
            "svg" => "image",
            "webp" => "image",
            "gif" => "image",
            "mp4" => "video",
            "mpg" => "video",
            "mpeg" => "video",
            "webm" => "video",
            "ogg" => "video",
            "avi" => "video",
            "mov" => "video",
            "flv" => "video",
            "swf" => "video",
            "mkv" => "video",
            "wmv" => "video",
            "wma" => "audio",
            "aac" => "audio",
            "wav" => "audio",
            "mp3" => "audio",
            "zip" => "archive",
            "rar" => "archive",
            "7z" => "archive",
            "doc" => "document",
            "txt" => "document",
            "docx" => "document",
            "pdf" => "document",
            "csv" => "document",
            "xml" => "document",
            "ods" => "document",
            "xlr" => "document",
            "xls" => "document",
            "xlsx" => "document"
        );

        if (!$request->hasFile('aiz_file') || !$request->file('aiz_file')->isValid()) {
            return response()->json(['error' => 'Invalid file'], 400);
        }

        $file = $request->file('aiz_file');
        $extension = strtolower($file->getClientOriginalExtension());

        if (!isset($type[$extension])) {
            return response()->json(['error' => 'Unsupported file type'], 400);
        }

        $upload = new Upload;

        // Original file name
        $upload->file_original_name = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $upload->extension = $extension;
        $upload->type = $type[$extension];
        $upload->user_id = Auth::id();
        $size = $file->getSize();

        // Prepare temp file
        $tempPath = $file->store('temp-uploads', 'local');
        $fullTempPath = storage_path("app/{$tempPath}");

        // SVG Sanitization
        if ($extension === 'svg') {
            try {
                $sanitizer = new \enshrined\svgSanitize\Sanitizer();
                $dirtySVG = file_get_contents($fullTempPath);
                $cleanSVG = $sanitizer->sanitize($dirtySVG);
                file_put_contents($fullTempPath, $cleanSVG);
                clearstatcache();
                $size = filesize($fullTempPath);
            } catch (\Exception $e) {
                \Log::error('SVG sanitization failed: ' . $e->getMessage());
            }
        }

        // Image Optimization
        if ($upload->type === 'image' && get_setting('disable_image_optimization') != 1) {
            try {
                $img = \Image::make($fullTempPath)->encode();
                $height = $img->height();
                $width = $img->width();

                if ($width > $height && $width > 1500) {
                    $img->resize(1500, null, function ($constraint) {
                        $constraint->aspectRatio();
                    });
                } elseif ($height > 1500) {
                    $img->resize(null, 800, function ($constraint) {
                        $constraint->aspectRatio();
                    });
                }

                $img->save($fullTempPath);
                clearstatcache();
                $size = filesize($fullTempPath);
            } catch (\Exception $e) {
                \Log::error('Image optimization failed: ' . $e->getMessage());
            }
        }

        // Upload to Spaces (or any S3 compatible disk)
        $fileName = uniqid() . '.' . $extension;
        $path = "nahid/all/{$fileName}";

        if (!file_exists($fullTempPath)) {
            \Log::error("Temp file not found at: " . $fullTempPath);
            return response()->json(['error' => 'Temporary file missing'], 500);
        }

        $stream = fopen($fullTempPath, 'r');

        Storage::disk('spaces')->put($path, $stream, [
            'visibility' => 'public',
            'ContentType' => $extension === 'svg' ? 'image/svg+xml' : $file->getMimeType()
        ]);

        fclose($stream);

        // Clean up temp file
        Storage::disk('local')->delete($tempPath);

        // Save upload record
        $upload->file_name = $path;
        $upload->file_size = $size;
        $upload->save();

        return response()->json(['success' => true, 'path' => $path], 200);
    }

    public function get_uploaded_files(Request $request)
    {
        $uploads = Upload::where('user_id', Auth::user()->id);
        if ($request->search != null) {
            $uploads->where('file_original_name', 'like', '%' . $request->search . '%');
        }
        if ($request->sort != null) {
            switch ($request->sort) {
                case 'newest':
                    $uploads->orderBy('created_at', 'desc');
                    break;
                case 'oldest':
                    $uploads->orderBy('created_at', 'asc');
                    break;
                case 'smallest':
                    $uploads->orderBy('file_size', 'asc');
                    break;
                case 'largest':
                    $uploads->orderBy('file_size', 'desc');
                    break;
                default:
                    $uploads->orderBy('created_at', 'desc');
                    break;
            }
        }
        log::error('Uploads: ' . print_r($uploads->paginate(60)->appends(request()->query()), true));
        return $uploads->paginate(60)->appends(request()->query());
    }

    public function destroy($id)
    {
        $upload = Upload::findOrFail($id);

        if (auth()->user()->user_type == 'seller' && $upload->user_id != auth()->user()->id) {
            flash(translate("You don't have permission for deleting this!"))->error();
            return back();
        }
        try {
            if (env('FILESYSTEM_DRIVER') != 'local') {
                Storage::disk(env('FILESYSTEM_DRIVER'))->delete($upload->file_name);
                if (file_exists(public_path() . '/' . $upload->file_name)) {
                    unlink(public_path() . '/' . $upload->file_name);
                }
            } else {
                unlink(public_path() . '/' . $upload->file_name);
            }
            $upload->delete();
            flash(translate('File deleted successfully'))->success();
        } catch (\Exception $e) {
            $upload->delete();
            flash(translate('File deleted successfully'))->success();
        }
        return back();
    }

    public function bulk_uploaded_files_delete(Request $request)
    {
        if ($request->id) {
            foreach ($request->id as $file_id) {
                $this->destroy($file_id);
            }
            return 1;
        } else {
            return 0;
        }
    }

    public function get_preview_files(Request $request)
    {
        $ids = explode(',', $request->ids);
        $files = Upload::whereIn('id', $ids)->get();
        $new_file_array = [];
        foreach ($files as $file) {
            $file['file_name'] = my_asset($file->file_name);
            if ($file->external_link) {
                $file['file_name'] = $file->external_link;
            }
            $new_file_array[] = $file;
        }
        // dd($new_file_array);
        return $new_file_array;
        // return $files;
    }

    public function all_file()
    {
        $uploads = Upload::all();
        foreach ($uploads as $upload) {
            try {
                if (env('FILESYSTEM_DRIVER') != 'local') {
                    Storage::disk(env('FILESYSTEM_DRIVER'))->delete($upload->file_name);
                    if (file_exists(public_path() . '/' . $upload->file_name)) {
                        unlink(public_path() . '/' . $upload->file_name);
                    }
                } else {
                    unlink(public_path() . '/' . $upload->file_name);
                }
                $upload->delete();
                flash(translate('File deleted successfully'))->success();
            } catch (\Exception $e) {
                $upload->delete();
                flash(translate('File deleted successfully'))->success();
            }
        }

        Upload::query()->truncate();

        return back();
    }

    //Download project attachment
    public function attachment_download($id)
    {
        $project_attachment = Upload::find($id);
        try {
            $file_path = public_path($project_attachment->file_name);
            return Response::download($file_path);
        } catch (\Exception $e) {
            flash(translate('File does not exist!'))->error();
            return back();
        }
    }
    //Download project attachment
    public function file_info(Request $request)
    {
        $file = Upload::findOrFail($request['id']);

        return (auth()->user()->user_type == 'seller')
            ? view('seller.uploads.info', compact('file'))
            : view('backend.uploaded_files.info', compact('file'));
    }
}
