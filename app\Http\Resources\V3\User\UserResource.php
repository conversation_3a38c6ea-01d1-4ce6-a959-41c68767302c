<?php

namespace App\Http\Resources\V3\User;

use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $commonFields = [
            'id' => (int)$this->id,
            'type' => (string)$this->user_type,
            'name' => (string)$this->name,
            'email' => (string)$this->email,
            'avatar' => (string)$this->avatar,
            'profile_picture' => (string)uploaded_asset($this->avatar_original),
            'phone' => (string)$this->phone,
            'email_verification_token' => (string)$this->email_verification_token,
            'email_verified' => (bool)$this->email_verified_at != null,
            'email_verified_at' => (string)$this->email_verified_at,
            'role' => (string)$this->user_type,
            'userType' => (string)$this->user_type,
            'user_type' => (string)$this->user_type,
        ];
        if($request->route()->getActionMethod()==='profile'){
            return array_merge($commonFields,[
                'notifications_enabled' => (bool)$this->notifications_enabled,
            ]);
        }else{
            return $commonFields;
        }
    }
}
