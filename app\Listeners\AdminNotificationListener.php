<?php

namespace App\Listeners;

use App\Events\AdminNotificationEvent;
use App\Services\NotificationService;
use App\Enums\NotificationType;
use App\Enums\NotificationPriority;

class AdminNotificationListener
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Handle various admin notification events
     */
    public function handle($event)
    {
        // Handle different types of events
        switch (get_class($event)) {
            case 'App\Events\OrderCreated':
                $this->handleOrderCreated($event);
                break;
            
            case 'App\Events\PaymentReceived':
                $this->handlePaymentReceived($event);
                break;
            
            case 'App\Events\TicketCreated':
                $this->handleTicketCreated($event);
                break;
            
            case 'App\Events\UserRegistered':
                $this->handleUserRegistered($event);
                break;
            
            case 'App\Events\ProductStockLow':
                $this->handleStockAlert($event);
                break;
        }
    }

    /**
     * Handle order created event
     */
    private function handleOrderCreated($event)
    {
        $order = $event->order;
        
        $this->notificationService->sendAdminNotification(
            title: "New Order Alert - #{$order->id}",
            message: "New order placed by {$order->user->name} worth " . single_price($order->grand_total),
            type: NotificationType::ORDER,
            priority: NotificationPriority::HIGH,
            link: route('all_orders.show', $order->id),
            linkText: 'View Order',
            subject: $order,
            userId: $order->user_id
        );
    }

    /**
     * Handle payment received event
     */
    private function handlePaymentReceived($event)
    {
        $payment = $event->payment;
        
        $this->notificationService->sendAdminNotification(
            title: "Payment Received - #{$payment->order_id}",
            message: "Payment of " . single_price($payment->amount) . " received for order #{$payment->order_id}",
            type: NotificationType::PAYMENT,
            priority: NotificationPriority::HIGH,
            link: route('all_orders.show', $payment->order_id),
            linkText: 'View Order',
            subject: $payment,
            userId: $payment->user_id ?? null
        );
    }

    /**
     * Handle support ticket created event
     */
    private function handleTicketCreated($event)
    {
        $ticket = $event->ticket;
        
        $this->notificationService->sendAdminNotification(
            title: "New Support Ticket - #{$ticket->id}",
            message: "New support ticket '{$ticket->subject}' created by {$ticket->user->name}",
            type: NotificationType::SUPPORT,
            priority: $this->getTicketPriority($ticket->priority ?? 'medium'),
            link: route('support_ticket.admin_show', $ticket->id),
            linkText: 'View Ticket',
            subject: $ticket,
            userId: $ticket->user_id
        );
    }

    /**
     * Handle user registration event
     */
    private function handleUserRegistered($event)
    {
        $user = $event->user;
        
        if ($user->user_type !== 'admin') {
            $this->notificationService->sendAdminNotification(
                title: "New User Registration",
                message: "New {$user->user_type} '{$user->name}' registered",
                type: NotificationType::ACCOUNT,
                priority: NotificationPriority::LOW,
                link: $user->user_type === 'customer' ? route('customers.show', $user->id) : route('sellers.show', $user->id),
                linkText: 'View User',
                subject: $user,
                userId: $user->id
            );
        }
    }

    /**
     * Handle stock alert event
     */
    private function handleStockAlert($event)
    {
        $product = $event->product;
        $alertType = $event->alertType ?? 'low_stock';
        
        $title = match($alertType) {
            'out_of_stock' => 'Out of Stock Alert',
            'low_stock' => 'Low Stock Alert',
            default => 'Stock Alert'
        };
        
        $message = match($alertType) {
            'out_of_stock' => "Product '{$product->name}' is out of stock!",
            'low_stock' => "Product '{$product->name}' is running low on stock. Current: {$product->current_stock}",
            default => "Stock alert for product '{$product->name}'"
        };
        
        $priority = $alertType === 'out_of_stock' ? NotificationPriority::URGENT : NotificationPriority::HIGH;
        
        $this->notificationService->sendAdminNotification(
            title: $title,
            message: $message,
            type: NotificationType::STOCK,
            priority: $priority,
            link: route('products.admin.edit', $product->id),
            linkText: 'Update Stock',
            subject: $product,
            userId: $product->user_id
        );
    }

    /**
     * Get notification priority based on ticket priority
     */
    private function getTicketPriority(string $ticketPriority): string
    {
        return match(strtolower($ticketPriority)) {
            'high', 'urgent' => NotificationPriority::HIGH,
            'low' => NotificationPriority::LOW,
            default => NotificationPriority::MEDIUM,
        };
    }
} 