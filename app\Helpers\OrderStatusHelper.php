<?php

namespace App\Helpers;

use App\Enums\OrderStatus;

class OrderStatusHelper
{
    /**
     * Payment status mapping
     */
    private static $paymentStatusMap = [
        'paid' => 'Paid',
        'unpaid' => 'Unpaid',
        'pending' => 'Pending',
        'cancelled' => 'Cancelled',
        'refunded' => 'Refunded',
        'partial_payment' => 'Partial Payment'
    ];

    /**
     * Convert delivery status key to human-readable label using OrderStatus enum
     *
     * @param string $status
     * @return string
     */
    public static function getReadableDeliveryStatus($status)
    {
        if (!$status) {
            return 'Pending';
        }

        $key = strtolower(trim($status));
        
        // Use OrderStatus enum for mapping
        return OrderStatus::getLabel($key);
    }

    /**
     * Convert payment status key to human-readable label
     *
     * @param string $status
     * @return string
     */
    public static function getReadablePaymentStatus($status)
    {
        if (!$status) {
            return 'Pending';
        }

        $key = strtolower(trim($status));
        
        return self::$paymentStatusMap[$key] ?? ucfirst(str_replace('_', ' ', $status));
    }

    /**
     * Get all delivery status options using OrderStatus enum
     *
     * @return array
     */
    public static function getAllDeliveryStatuses()
    {
        return OrderStatus::getLabels();
    }

    /**
     * Get all payment status options
     *
     * @return array
     */
    public static function getAllPaymentStatuses()
    {
        return self::$paymentStatusMap;
    }

    /**
     * Get delivery status color/badge class for UI with proper color mapping
     *
     * @param string $status
     * @return string
     */
    public static function getDeliveryStatusBadgeClass($status)
    {
        $key = strtolower(trim($status));
        
        switch ($key) {
            case OrderStatus::DELIVERED:
                return 'success'; // Green
            case OrderStatus::ON_THE_WAY:
                return 'primary'; // Blue
            case OrderStatus::PICKED_UP:
                return 'info'; // Light Blue
            case OrderStatus::CONFIRMED:
                return 'warning'; // Yellow/Orange
            case OrderStatus::ORDER_PLACED:
                return 'secondary'; // Gray
            case OrderStatus::PENDING:
                return 'light'; // Light Gray
            case OrderStatus::CANCELLED:
                return 'danger'; // Red
            case OrderStatus::RETURNED_DELIVERY:
            case OrderStatus::RETURN:
                return 'dark'; // Dark Gray
            case OrderStatus::REFUND:
                return 'danger'; // Red
            case OrderStatus::RESHIPPED:
                return 'info'; // Light Blue
            default:
                return 'secondary'; // Gray fallback
        }
    }

    /**
     * Get payment status color/badge class for UI
     *
     * @param string $status
     * @return string
     */
    public static function getPaymentStatusBadgeClass($status)
    {
        $key = strtolower(trim($status));
        
        switch ($key) {
            case 'paid':
                return 'success'; // Green
            case 'cancelled':
            case 'refunded':
                return 'danger'; // Red
            case 'partial_payment':
                return 'warning'; // Yellow/Orange
            case 'pending':
                return 'info'; // Light Blue
            case 'unpaid':
            default:
                return 'secondary'; // Gray
        }
    }

    /**
     * Get delivery status with styled badge HTML
     *
     * @param string $status
     * @return string
     */
    public static function getDeliveryStatusBadge($status)
    {
        $label = self::getReadableDeliveryStatus($status);
        $class = self::getDeliveryStatusBadgeClass($status);
        
        return "<span class=\"badge bg-{$class}\">{$label}</span>";
    }

    /**
     * Get payment status with styled badge HTML
     *
     * @param string $status
     * @return string
     */
    public static function getPaymentStatusBadge($status)
    {
        $label = self::getReadablePaymentStatus($status);
        $class = self::getPaymentStatusBadgeClass($status);
        
        return "<span class=\"badge bg-{$class}\">{$label}</span>";
    }
} 