<?php

namespace App\Imports;

use DB;
use Artisan;
use App\Models\User;
use App\Models\Product;
use App\Models\ProductStock;
use App\Models\Upload;
use App\Models\Cart;
use App\Models\Wishlist;
use App\Models\ImportStatus;
use App\Services\ProductService;
use App\Services\ProductTaxService;
use App\Services\ProductFlashDealService;
use App\Services\ProductStockService;
use Illuminate\Support\Str;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithCalculatedFormulas;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;

class ProductBulkImport implements ToCollection, WithHeadingRow, WithChunkReading, WithCalculatedFormulas, SkipsEmptyRows
{
    private $importStatus;
    private $userId;
    private $rows = 0;
    private $successCount = 0;
    private $errorCount = 0;
    public $errors = [];
    public $rejectItem = [];

    public function __construct(ImportStatus $importStatus, $userId)
    {
        $this->userId = $userId;
        $this->importStatus = $importStatus;
    }

    public function transformHeader(array $header): array
    {
        return array_map(function($item) {
            return Str::snake(strtolower($item));
        }, $header);
    }

    public function collection(Collection $rows)
    {
        $user = User::where('id', $this->userId)->first();
        $canImport = true;

        if (!$user) {
            Log::error('Error in : User Not Found.');
            $this->importStatus->update([
                    'status' => 'failed',
                    'error_messages' => ['Unauthorized.'],
                ]);
            return back();
        }

        Log::info('User type : ' . $user->user_type);
        $totalRows = count($rows);
        $this->importStatus->update(['status' => 'in_progress']);

        if ($user->user_type == 'seller' && addon_is_activated('seller_subscription')) {
            if ((count($rows) + $user->products()->count()) > $user->shop->product_upload_limit
                || $user->shop->package_invalid_at == null
                || now()->diffInDays($user->shop->package_invalid_at, false) < 0
            ) {
                $canImport = false;
                flash()->warning(trans('Please upgrade your package.'));
                $this->importStatus->update([
                    'status' => 'failed',
                    'error_messages' => ['Please upgrade your package.'],
                ]);
                return redirect()->back();
            }
        }

        if ($canImport) {
            foreach ($rows as $row) {
                if (($this->importStatus->purpose === 'insert' && $row->has('id')) || (($this->importStatus->purpose === 'update' or $this->importStatus->purpose === 'delete') && !$row->has('id'))) {
                        $this->importStatus->update([
                            'status' => 'failed',
                            'error_messages' => ['Data Format is wrong, Please check your xlsx file.'],
                        ]);
                    return back();
                }

                if($row->has('dropshipper_price')){
                    $row['dropshipper_price'] = $row['dropshipper_price'] ? (double)$row['dropshipper_price'] : 0.00;
                }

                if($row->has('dropshipper_price')){
                    $row['unit_price'] = $row['unit_price'] ? (double)$row['unit_price'] : 0.00;
                }
                
                if ($this->importStatus->purpose === 'insert') {

                    $validator = Validator::make($row->toArray(), $this->rules());
                    if ($validator->fails()) {
                        $this->errors[] = $validator->errors()->all();
                        $this->rejectItem[] = $row['sku'] ??  $row['name'];
                        Log::warning('Validation failed for row, Errors: ' . json_encode($validator->errors()));
                        $this->errorCount++;
                    } else {
                        $this->processRow($row);
                        $this->successCount++;
                    }
                }else if($this->importStatus->purpose === 'update'){
                        $this->processRow($row);
                }else {
                    $this->deleteProduct($row['id']);
                    $this->successCount++;
                }
                $this->rows++;
            }

            // Update the import status
            $this->importStatus->update([
                'reject_item' => $this->rejectItem,
                'success' => $this->successCount,
                'errors' => $this->errorCount,
                'pending' => $this->importStatus->total - ($this->successCount + $this->errorCount),
                'status' => $this->rows === $this->importStatus->total ? 'completed' : 'in_progress',
                'progress' => (($this->successCount + $this->errorCount) / $this->importStatus->total) * 100,
                'error_messages' => $this->errors,
            ]);
        }
        Log::info('Finished chunk import of collection');
    }

    private function processRow($row)
    {
        try {
            if ($row->has('id') && $this->importStatus->purpose === 'update') {
                $this->updateProduct($row);
            } else {
                $this->createProduct($row);
            }
        } catch (\Exception $e) {
            Log::error('Error in processRow: ' .$this->errorCount. ' '. $e->getMessage());
            $this->errorCount++;
        }
    }

    public function createProduct($data)
    {
        $collection = collect($data);
        $user = User::find($this->userId);

        $approved = 1;
        if ($user->user_type == 'seller') {
            $user_id = $user->id;
            if (get_setting('product_approve_by_admin') == 1) {
                $approved = 0;
            }
        } else {
            $user_id = User::where('user_type', 'admin')->first()->id;
        }

        if ($collection->has('added_by')) {
            $collection['added_by'] = $collection['added_by'] ?? 'admin';
        }

        
        $collection['unit_price'] = (double)$collection['unit_price'] ?? 0.00;
        $collection['dropshipper_price'] = (double)$collection['dropshipper_price'] ?? 0.00;
        $collection['variant_product'] = $collection['variant_product'] ?? 0;
        $collection['attributes'] = $collection['attributes'] ?? '[]';
        $collection['todays_deal'] = $collection['todays_deal'] ?? 0;
        $collection['published'] = $collection['published'] ?? 1;
        $collection['approved'] = $collection['approved'] ?? 1;
        $collection['stock_visibility_state'] = $collection['stock_visibility_state'] ?? 'quantity';
        $collection['cash_on_delivery'] = $collection['cash_on_delivery'] ?? 0;
        $collection['featured'] = $collection['featured'] ?? 0;
        $collection['seller_featured'] = $collection['seller_featured'] ?? 0;
        $collection['current_stock'] = $collection['current_stock'] ?? 0;
        $collection['weight'] = $collection['weight'] ?? 0.00;
        $collection['allow_customer_review'] = $collection['allow_customer_review'] ?? 1;
        $collection['min_qty'] = $collection['min_qty'] ?? 1;
        $collection['shipping_type'] = $collection['shipping_type'] ?? 'flat_rate';
        $collection['shipping_cost'] = $collection['shipping_cost'] ?? 0.00;
        $collection['is_quantity_multiplied'] = $collection['is_quantity_multiplied'] ?? 0;
        $collection['num_of_sale'] = $collection['num_of_sale'] ?? 0;
        $collection['refundable'] = $collection['refundable'] ?? 0;
        $collection['rating'] = $collection['rating'] ?? 0.00;
        $collection['digital'] = $collection['digital'] ?? 0;
        $collection['auction_product'] = $collection['auction_product'] ?? 0;
        $collection['wholesale_product'] = $collection['wholesale_product'] ?? 0;
        $collection['sold_individually'] = $collection['sold_individually'] ?? 0;
        $collection['visibility_in_catalog'] = $collection['visibility_in_catalog'] ?? 0;
        $collection['grouped_product'] = $collection['grouped_product'] ?? 0;
        $collection['backorders_allowed'] = $collection['backorders_allowed'] ?? 0;

        if ($collection->has('name') or $collection->has('meta_title')) {
            $collection->put('meta_title', $collection->get('meta_title') ?? $collection->get('name'));
        }
        
        if ($collection->has('meta_description') or $collection->has('description')) {
            $collection->put('meta_description', $collection->get('meta_description') ?? strip_tags($collection->get('description')));
        }
        
        if ($collection->has('meta_img') or $collection->has('thumbnail_img')) {
            $collection->put('meta_img', $collection->get('meta_img') ?? $collection->get('thumbnail_img'));
        }
        

        $collection->put('colors', json_encode([]));
        $collection->put('attributes', json_encode([]));
        $collection->put('choice_options', json_encode([]));

        // $discount_start_date = $this->parseDateRange($collection->get('date_range'))['start'];
        // $discount_end_date = $this->parseDateRange($collection->get('date_range'))['end'];

        $collection['discount_start_date'] = strtotime($collection->get('discount_start_date'));

        $collection['discount_end_date'] = strtotime($collection->get('discount_end_date'));

        if ($collection->has('thumbnail_img') && filter_var($collection->get('thumbnail_img'), FILTER_VALIDATE_URL)) {
            $thumbnail_id = $this->downloadThumbnail($collection->get('thumbnail_img'));
            if ($thumbnail_id) {
                $collection->put('thumbnail_img', $thumbnail_id);
            }
        }

        if ($collection->has('photos') && !empty($collection->get('photos'))) {
            $gallery_images = $this->downloadGalleryImages($collection->get('photos'));
            $collection->put('photos', $gallery_images);
        }
       
        $shipping_cost = $this->calculateShippingCost($collection);

        $slug = $this->generateSlug($collection->get('name'));
        // $published = $this->determinePublishStatus($collection->get('button'));

        $data = $collection->merge(compact(
            'user_id',
            'approved',
            'shipping_cost',
            'slug',
        ))->toArray();
        Log::info('test for data: ' .$data['name'].' '.$data['dropshipper_price']);

        try {
            $product = Product::create($data);

            $product_stocks = ProductStock::create([
                'product_id' => $product->id,
                'variant' => "",
                'sku' => $data['sku'] ?? null,
                'upc' => $data['upc'] ?? null,
                'price' => $data['unit_price'] ?? 0.00,
                'dropshipper_price' => $data['dropshipper_price'] ?? 0.00,
                'qty' => $data['current_stock'] ?? 0,
                'image' => null,
            ]);
            Log::info('Product successfully stored');
        }catch (\Exception $e){
            $this->errors[] = $e->getMessage();
            $this->rejectItem[] = $data['sku'];
            Log::error('Error for data store: ' . $data['name']);
            Log::error('Error message: ' . $e->getMessage());

        }
    }


    public function updateProduct($data)
    {
       $collection = collect($data);
        $user = User::find($this->userId);
        Log::info('update User type : ' . $user->user_type);
        $approved = 1;
        if ($user->user_type == 'seller') {
            $user_id = $user->id;
            if (get_setting('product_approve_by_admin') == 1) {
                $approved = 0;
            }
        } else {
            $user_id = User::where('user_type', 'admin')->first()->id;
        }
        $product = Product::find($collection->get('id'));
        
        if ($collection->has('meta_img') or $collection->has('thumbnail_img')) {
            $collection->put('meta_img', $collection->get('meta_img') ?? $collection->get('thumbnail_img'));
        }

        if ($collection->has('meta_img') or $collection->has('thumbnail_img')) {
            $collection->put('meta_img', $collection->get('meta_img') ?? $collection->get('thumbnail_img'));
        }
        $collection->put('colors', json_encode([]));
        $collection->put('attributes', json_encode([]));
        $collection->put('choice_options', json_encode([]));
        
        if ($collection->has('discount_start_date')){
            $collection['discount_start_date'] = strtotime($collection->get('discount_start_date'));
        }
        if($collection->has('discount_end_date')) {
            $collection['discount_end_date'] = strtotime($collection->get('discount_end_date'));
        }
        
        
        if ($collection->has('thumbnail_img') && filter_var($collection->get('thumbnail_img'), FILTER_VALIDATE_URL)) {
            $thumbnail_id = $this->downloadThumbnail($collection->get('thumbnail_img'));
            if ($thumbnail_id) {
                $collection->put('thumbnail_img', $thumbnail_id);
            }
        }

        if ($collection->has('photos') && !empty($collection->get('photos'))) {
            $gallery_images = $this->downloadGalleryImages($collection->get('photos'));
            $collection->put('photos', $gallery_images);
        }

        $data = $collection->merge(compact(
            'user_id',
            'approved',
            'product'
        ))->toArray();

        // $unit_price = is_double($collection->get('unit_price')) ? (double)$collection->get('unit_price') : (double)$data['product']['unit_price'];
        // $dropshipper_price = is_double($collection->get('dropshipper_price')) ? (double)$collection->get('dropshipper_price') : (double)$data['product']['dropshipper_price'];

        // Log::info('update check: '.$unit_price);
        try{
            // if ((double)$unit_price > (double)$dropshipper_price) {
                if ($product) {
                    $product->update($data);
                    $product_stocks = ProductStock::where('product_id', $product->id)->first();
                    $stock_sku = $collection->has('sku') ? $collection->get('sku') : $product_stocks->sku;
                    $stock_upc = $collection->has('upc') ? $collection->get('upc') : $product_stocks->upc;
                    $stock_price = $collection->has('unit_price') ? $collection->get('unit_price') : $product_stocks->price;
                    $stock_dropshipper_price = $collection->has('dropshipper_price') ? $collection->get('dropshipper_price') : $product_stocks->dropshipper_price;
                    $stock_qty = $collection->has('current_stock') ? $collection->get('current_stock') : $product_stocks->qty;
                    Log::info('stock_sku-'.$stock_sku.'stock_upc-'.$stock_upc.'stock_price-'.$stock_price);
                    if ($product_stocks) {
                        $product_stocks->update([
                            'sku' => $stock_sku,
                            'price' => $stock_price,
                            'dropshipper_price' => $stock_dropshipper_price,
                            'qty' => $stock_qty,
                            'upc' => $stock_upc
                        ]);
                        $this->successCount++;
                    } else {
                        $this->errors[] = ["Product stock item not found."];
                        $this->rejectItem[] = $data['sku'] ?? $product->id;
                        $this->errorCount++;
                    }
                }
                else{
                    $this->errors[] = ["Product item not found."];
                    $this->rejectItem[] = $data['sku'] ?? $product->id ;
                    $this->errorCount++;
                }
            
            // else{
            //     $this->errors[] = ["dropshipper price grater than unit price."];
            //     $this->rejectItem[] = $data['sku'] ??  $collection['id'];
            //     $this->errorCount++;
            // }
        }catch(\Exception $e) {
            $this->errors[] = $e->getMessage();
            $this->rejectItem[] = $data['sku'];
            Log::error('Error for data store: ' . $e->getMessage());
        }
    }

    public function deleteProduct($id)
    {
        Log::info('value data: '.$id);
        $product = Product::with(['stocks'])->where('id',$id)->first();
        try{
            DB::beginTransaction(); // Start transaction
            if ($product) {
                $product->product_translations()->delete();
                $product->stocks()->delete();
                $product->taxes()->delete();

                if (Product::destroy($id)) {
                    Cart::where('product_id', $id)->delete();
                    Wishlist::where('product_id', $id)->delete();
                }
            }
            DB::commit();
        }catch(\Exception $e) {
            DB::rollBack();
            $this->errors[] = $e->getMessage();
            Log::error('Error for data delete: ' . $e->getMessage());
            $this->rejectItem[] = ''.$id;
        }
    }

    public function downloadThumbnail($url)
    {
        try {
            // if ($this->importStatus->purpose == 'update') {
            $upload = Upload::where('external_link',$url)->first();

            if ($upload) {
                return $upload->id;
            } 
            else {
                $start = strpos($url, '/uploads'); // find the position of /uploads
                $file_path = $start !== false ?? substr($url, $start); // regenarate the file path name
                Log::info("File path:". $file_path);
                $upload = Upload::where('file_name', $file_path)->first();
                
                if ($upload) {
                    return $upload->id;
                }
            }
            // }
            $upload = new Upload();
            $upload->external_link = $url;
            $upload->type = 'image';
            $upload->save();
            return $upload->id;
        } catch (\Exception $e) {
            Log::error('Error : ' . $e->getMessage());
        }
        return null;
    }

    public function downloadGalleryImages($urls)

    {
        $data = [];
        foreach (explode(',', str_replace(' ', '', $urls)) as $url) {
            $data[] = $this->downloadThumbnail($url);
        }
        return implode(',', $data);
    }

    private function parseDateRange($dateRange)
    {
        if ($dateRange) {
            $dates = explode(" to ", $dateRange);
            return [
                'start' => strtotime($dates[0]),
                'end' => strtotime($dates[1])
            ];
        }
        return ['start' => null, 'end' => null];
    }

    private function calculateShippingCost($collection)
    {
        if ($collection->get('shipping_type') == 'free') {
            return 0;
        } elseif ($collection->get('shipping_type') == 'flat_rate') {
            return $collection->get('flat_shipping_cost', 0);
        }
        return 0;
    }

    private function generateSlug($name)
    {
        $slug = Str::slug($name);
        $same_slug_count = Product::where('slug', 'LIKE', $slug . '%')->count();
        return $same_slug_count ? $slug . '-' . ($same_slug_count + 1) : $slug;
    }

    private function determinePublishStatus($button)
    {
        return in_array($button, ['unpublish', 'draft']) ? 0 : 1;
    }

    private function convertToTimestamp($date)
    {
        return $date ? strtotime($date) : null;
    }

    private function processColors($collection)
    {
        if ($collection->get('colors_active') && $collection->get('colors')) {
            return json_encode($collection->get('colors'));
        }
        return json_encode([]);
    }

    private function processChoiceOptions($collection)
    {
        $choice_options = [];
        if ($collection->has('choice_no')) {
            foreach ($collection->get('choice_no') as $no) {
                $attribute_data = $collection->get('choice_options_' . $no, []);
                $choice_options[] = [
                    'attribute_id' => $no,
                    'values' => $attribute_data
                ];
                $collection->forget('choice_options_' . $no);
            }
        }
        return json_encode($choice_options, JSON_UNESCAPED_UNICODE);
    }

    public function model(array $row)
    {
        ++$this->rows;
    }

    public function getRowCount(): int
    {
        return $this->rows;
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:200'],
            'category_id' => ['required', 'integer'],
            'min_qty' => ['required','numeric','min:0'],
            'unit_price' => ['required', 'numeric', 'min:0'],
            'thumbnail_img' => ['required', 'string'],
            'dropshipper_price' => ['numeric','lt:unit_price'],
        ];
    }

    public function chunkSize(): int
    {
        return 100;
    }

    public function headingRow(): int
    {
        return 1;
    }
}
