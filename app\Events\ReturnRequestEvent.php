<?php

namespace App\Events;

use App\Models\ReturnRequestInfo;
use App\Models\UserNotification;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ReturnRequestEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $returnRequest;
    public $notification;
    public $action; // 'created', 'updated', 'approved', 'rejected'

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(ReturnRequestInfo $returnRequest, $action = 'created', UserNotification $notification = null)
    {
        $this->returnRequest = $returnRequest;
        $this->action = $action;
        $this->notification = $notification;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        $channels = [
            new PrivateChannel('admin.notifications'), // Notify admin
        ];

        // If notification is for user, add user channel
        if ($this->notification && $this->notification->user_id) {
            $channels[] = new PrivateChannel('notifications.' . $this->notification->user_id);
        }

        // If return request has an order, notify the customer
        if ($this->returnRequest->order && $this->action !== 'created') {
            $channels[] = new PrivateChannel('notifications.' . $this->returnRequest->order->user_id);
        }

        return $channels;
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs()
    {
        return 'return.request.' . $this->action;
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        $data = [
            'return_request_id' => $this->returnRequest->id,
            'action' => $this->action,
            'timestamp' => now()->toIso8601String(),
            'return_request' => [
                'id' => $this->returnRequest->id,
                'order_id' => $this->returnRequest->order_id,
                'status' => $this->returnRequest->status ?? 'pending',
                'reason' => $this->returnRequest->reason,
                'created_at' => $this->returnRequest->created_at->toIso8601String(),
            ]
        ];

        if ($this->notification) {
            $data['notification'] = [
                'id' => $this->notification->id,
                'type' => $this->notification->type,
                'title' => $this->notification->title,
                'message' => $this->notification->message,
                'date' => $this->notification->created_at->toIso8601String(),
                'read' => (bool) $this->notification->read,
                'priority' => $this->notification->priority,
                'link' => $this->notification->link,
                'linkText' => $this->notification->link_text,
            ];
        }

        return $data;
    }
} 