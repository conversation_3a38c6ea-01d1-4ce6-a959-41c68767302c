<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\Coupon\CouponCategoryResource;
use App\Http\Resources\V3\Coupon\CouponResource;
use App\Http\Resources\V3\Coupon\ExpiringSoonCouponResource;
use App\Http\Resources\V3\Coupon\FeaturedCouponResource;
use App\Http\Resources\V3\Coupon\PersonalizedCouponResource;
use App\Models\Cart;
use App\Services\ApiCouponService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ApiCouponController extends ApiResponse
{
    protected ApiCouponService $apiCouponService;

    /**
     * Create a new controller instance.
     *
     * @param ApiCouponService $apiCouponService
     * @return void
     */
    public function __construct(ApiCouponService $apiCouponService)
    {
        parent::__construct();
        $this->apiCouponService = $apiCouponService;
    }

    /**
     * Get a paginated list of coupons with optional filtering
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // Validate request parameters
            $validator = Validator::make($request->all(), [
                'page' => 'nullable|integer|min:1',
                'perPage' => 'nullable|integer|min:1|max:100',
                'search' => 'nullable|string|max:255',
                'type' => 'nullable|string|in:product_base,category_base,cart_base',
                'isActive' => 'nullable|boolean',
                'sortBy' => 'nullable|string',
                'sortDirection' => 'nullable|string|in:asc,desc'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            // Get coupons with filters
            $filters = $request->only([
                'page',
                'perPage',
                'search',
                'type',
                'isActive',
                'sortBy',
                'sortDirection'
            ]);

            $coupons = $this->apiCouponService->getCoupons($filters);

            // Return paginated response
            return $this->success([
                'coupons' => CouponResource::collection($coupons->items()),
                'meta' => [
                    'currentPage' => $coupons->currentPage(),
                    'from' => $coupons->firstItem(),
                    'lastPage' => $coupons->lastPage(),
                    'perPage' => $coupons->perPage(),
                    'to' => $coupons->lastItem(),
                    'total' => $coupons->total()
                ]
            ]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to retrieve coupons',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get a single coupon by ID
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $coupon = $this->apiCouponService->getCouponById($id);

            if (!$coupon) {
                return $this->error(
                    'NOT_FOUND',
                    'Coupon not found',
                    null,
                    404
                );
            }

            return $this->success(
                new CouponResource($coupon)
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to retrieve coupon',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get featured coupons
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function featured(Request $request): JsonResponse
    {
        try {
            // Validate request parameters
            $validator = Validator::make($request->all(), [
                'limit' => 'nullable|integer|min:1|max:20'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            // Get limit parameter or use default
            $limit = $request->input('limit', 4);

            // Get featured coupons
            $coupons = $this->apiCouponService->getFeaturedCoupons($limit);

            // Return response
            return $this->success(
                FeaturedCouponResource::collection($coupons)
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to retrieve featured coupons',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get personalized coupons for the current user
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function personalized(Request $request): JsonResponse
    {
        try {
            // Validate request parameters
            $validator = Validator::make($request->all(), [
                'limit' => 'nullable|integer|min:1|max:20'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            // Get the authenticated user
            $user = auth()->user();

            if (!$user) {
                return $this->error(
                    'UNAUTHORIZED',
                    'Authentication required',
                    null,
                    401
                );
            }

            // Get limit parameter or use default
            $limit = $request->input('limit', 4);

            // Get personalized coupons
            $coupons = $this->apiCouponService->getPersonalizedCoupons($user->id, $limit);

            // Return response
            return $this->success(
                PersonalizedCouponResource::collection($coupons)
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to retrieve personalized coupons',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get coupons that are expiring soon
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function expiringSoon(Request $request): JsonResponse
    {
        try {
            // Validate request parameters
            $validator = Validator::make($request->all(), [
                'days' => 'nullable|integer|min:1|max:30',
                'limit' => 'nullable|integer|min:1|max:20'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            // Get parameters or use defaults
            $days = $request->input('days', 7);
            $limit = $request->input('limit', 4);

            // Get expiring soon coupons
            $coupons = $this->apiCouponService->getExpiringSoonCoupons($days, $limit);

            // Return response
            return $this->success(
                ExpiringSoonCouponResource::collection($coupons)
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to retrieve expiring soon coupons',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get categories that have associated coupons
     *
     * @return JsonResponse
     */
    public function categories(): JsonResponse
    {
        try {
            // Get categories with coupon counts
            $categories = $this->apiCouponService->getCouponCategories();

            // Return response
            return $this->success(
                CouponCategoryResource::collection($categories)
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to retrieve coupon categories',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get statistical information about coupons
     *
     * @return JsonResponse
     */
    public function stats(): JsonResponse
    {
        try {
            // Get coupon statistics
            $stats = $this->apiCouponService->getCouponStats();

            // Return response
            return $this->success($stats);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to retrieve coupon statistics',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Validate a coupon code for a potential order
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function validateCoupon(Request $request): JsonResponse
    {
        try {
            // Validate request parameters
            $validator = Validator::make($request->all(), [
                'code' => 'required|string|exists:coupons,code',
                'subtotal' => 'required|numeric|min:0',
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }
            $userId = Auth::id();
            // Get the coupon
            $coupon = \App\Models\Coupon::where('code', $request->code)->first();

            if (!$coupon) {
                return $this->error(
                    'COUPON_NOT_FOUND',
                    'Coupon not found',
                    null,
                    404
                );
            }

            // Check if coupon is within valid date range
            $inRange = strtotime(date('d-m-Y')) >= $coupon->start_date && strtotime(date('d-m-Y')) <= $coupon->end_date;

            if (!$inRange) {
                return $this->success([
                    'valid' => false,
                    'coupon' => new CouponResource($coupon),
                    'message' => 'Coupon has expired'
                ]);
            }

            // Check if user has already used this coupon
            $userId = auth('sanctum')->id();
            if ($userId) {
                $isUsed = \App\Models\CouponUsage::where('user_id', $userId)
                    ->where('coupon_id', $coupon->id)
                    ->first() != null;

                if ($isUsed) {
                    return $this->success([
                        'success' => false,
                        'coupon' => new CouponResource($coupon),
                        'message' => 'You have already used this coupon'
                    ]);
                }
            }

            // Calculate discount
            $couponDiscount = 0;
            $applicableItems = [];
            $couponDetails = json_decode($coupon->details);

            if ($coupon->type == 'cart_base') {
                // Cart-based coupon
                $orderAmount = $request->subtotal;

                if ($orderAmount >= $couponDetails->min_buy) {
                    if ($coupon->discount_type == 'percent') {
                        $couponDiscount = ($orderAmount * $coupon->discount) / 100;
                        if ($couponDiscount > $couponDetails->max_discount) {
                            $couponDiscount = $couponDetails->max_discount;
                        }
                    } elseif ($coupon->discount_type == 'amount') {
                        $couponDiscount = $coupon->discount;
                    }
                    $carts = Cart::where('user_id', $userId)->get();
                    // All items are applicable for cart-based coupons
                    foreach ($carts as $item) {
                        $itemDiscount = ($item['price'] * $item['quantity'] / $orderAmount) * $couponDiscount;
                        $applicableItems[] = [
                            'productId' => $item['product_id'],
                            'discountAmount' => round($itemDiscount, 2)
                        ];
                    }
                }
            } elseif ($coupon->type == 'product_base') {
                // Product-based coupon
                $carts = Cart::where('user_id', $userId)->get();
                foreach ($carts as $item) {
                    $productId = $item['product_id'];
                    $quantity = $item['quantity'];
                    $price = $item['price'];

                    foreach ($couponDetails as $couponDetail) {
                        if ($couponDetail->product_id == $productId) {
                            $itemDiscount = 0;

                            if ($coupon->discount_type == 'percent') {
                                $itemDiscount = ($price * $quantity * $coupon->discount) / 100;
                            } elseif ($coupon->discount_type == 'amount') {
                                $itemDiscount = $coupon->discount * $quantity;
                            }

                            $couponDiscount += $itemDiscount;

                            $applicableItems[] = [
                                'productId' => $productId,
                                'discountAmount' => round($itemDiscount, 2)
                            ];

                            break;
                        }
                    }
                }
            }

            // Round the discount to 2 decimal places
            $couponDiscount = round($couponDiscount, 2);

            // Return response
            if ($couponDiscount > 0) {
                return response()->json([
                    'success' => true,
                    'message' => 'Coupon is valid',
                    'discountAmount' => $couponDiscount,
                    'coupon' => $request->code, // optional: only if you want to include full coupon data
                ], 200);
              /*  return $this->success([
                    'valid' => true,
                    'coupon' => [
                        'id' => $coupon->id,
                        'code' => $coupon->code,
                        'type' => $coupon->discount_type == 'percent' ? 'percentage' : 'amount',
                        'value' => (float) $coupon->discount,
                        'description' => $coupon->description
                    ],
                    'discountAmount' => $couponDiscount,
                    'applicableItems' => $applicableItems
                ]);*/
            } else {
                return $this->success([
                    'success' => false,
                    'coupon' => new CouponResource($coupon),
                    'message' => 'This coupon is not applicable to your order'
                ]);
            }

        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to validate coupon',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get coupon usage report
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function usageReport(Request $request): JsonResponse
    {
        try {
            // Validate request parameters
            $validator = Validator::make($request->all(), [
                'period' => 'nullable|string|in:day,week,month,year',
                'dateFrom' => 'nullable|date_format:Y-m-d',
                'dateTo' => 'nullable|date_format:Y-m-d',
                'couponId' => 'nullable|integer|exists:coupons,id'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            // Get parameters
            $period = $request->input('period', 'month');
            $dateFrom = $request->input('dateFrom');
            $dateTo = $request->input('dateTo');
            $couponId = $request->input('couponId');

            // Get coupon usage report
            $report = $this->apiCouponService->getCouponUsageReport($period, $dateFrom, $dateTo, $couponId);


            // Return response
            return $this->success($report);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to retrieve coupon usage report',
                $e->getMessage(),
                500
            );
        }
    }
}
